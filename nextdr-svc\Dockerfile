# ===== Stage 1: Build Go agents =====
FROM golang:1.23 AS go-builder

WORKDIR /build

# Copy only go.mod/go.sum first for caching
COPY src/agents/go.mod ./agents/

# Download dependencies early
WORKDIR /build/agents
RUN go mod download

# Copy actual Go source
COPY src/agents ./agents

# Build binaries
RUN mkdir -p /app/agents \
    && cd agents/osupdate && go build -o /app/agents/osupdate-agent . \
    && cd ../viruscheck && go build -o /app/agents/virus-agent .

# ===== Stage 2: Build Node.js app =====
FROM node:20-alpine AS node-builder

WORKDIR /app

# Copy and install deps
COPY package*.json ./
RUN npm ci

# Copy code and build it
COPY . .
RUN npm run build

# ===== Stage 3: Final minimal image =====
FROM node:20-alpine AS final

WORKDIR /app

# Copy only what's needed
COPY --from=node-builder /app/dist ./dist
COPY --from=node-builder /app/node_modules ./node_modules
COPY --from=node-builder /app/package*.json ./
COPY --from=go-builder /app/agents ./agents

# Expose port and run app
EXPOSE 8081
CMD ["node", "dist/server.js"]