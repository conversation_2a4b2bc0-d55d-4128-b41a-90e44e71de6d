import { NextResponse } from "next/server";
import { createClient, isAdmin } from "@/lib/supabase/server";

export async function GET(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const supabase = await createClient();
		const { data: customers, error } = await supabase.from("customer_profiles").select("*");

		if (error) throw error;

		return NextResponse.json(customers);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}

export async function POST(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { email, password, company_name, contact_number, address } = await request.json();

		if (!email || !password || !company_name) {
			return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
		}

		const supabase = await createClient();

		// Create auth user
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email,
			password,
			email_confirm: true,
			user_metadata: { role: "customer" },
		});

		if (authError) throw authError;

		// Create customer profile
		const { data: profile, error: profileError } = await supabase
			.from("customer_profiles")
			.insert({
				id: authData.user.id,
				company_name,
				contact_number,
				address,
			})
			.select()
			.single();

		if (profileError) throw profileError;

		return NextResponse.json(profile);
	} catch (error: any) {
		console.error(error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
