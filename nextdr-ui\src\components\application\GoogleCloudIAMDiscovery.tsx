import React, { useState, useEffect } from "react";
import { User, RefreshCw, Shield, ChevronDown, ChevronRight, Key } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { getIAMAccountsRoles } from "@/lib/api/api-client";
import { IAMRole } from "@/lib/types";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Badge } from "@/components/ui/badge";

interface GoogleCloudIAMDiscoveryProps {
	projectId?: string;
	datacenterId: string;
}

interface ServiceAccount {
	email: string;
	roles: string[];
	isNextDR: boolean;
}

export default function GoogleCloudIAMDiscovery({
	projectId,
	datacenterId,
}: GoogleCloudIAMDiscoveryProps) {
	const [serviceAccounts, setServiceAccounts] = useState<ServiceAccount[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [expandedAccounts, setExpandedAccounts] = useState<Record<string, boolean>>({});
	const { toast } = useToast();

	const toggleAccountExpansion = (email: string) => {
		setExpandedAccounts((prev) => ({
			...prev,
			[email]: !prev[email],
		}));
	};

	const processIAMData = (iamRoles: IAMRole[]) => {
		const serviceAccountMap = new Map<string, ServiceAccount>();

		iamRoles.forEach((role) => {
			role.members.forEach((member) => {
				if (member.startsWith("serviceAccount:")) {
					const email = member.replace("serviceAccount:", "");
					if (!serviceAccountMap.has(email)) {
						serviceAccountMap.set(email, {
							email,
							roles: [],
							isNextDR: email.includes("nextdr") || email.includes("backupdr"),
						});
					}
					serviceAccountMap.get(email)?.roles.push(role.role);
				}
			});
		});

		return Array.from(serviceAccountMap.values());
	};

	const fetchIAMAccounts = async () => {
		if (!projectId) return;

		setLoading(true);
		setError(null);
		try {
			const data: IAMRole[] = await getIAMAccountsRoles(projectId, datacenterId);
			const processedData = processIAMData(data);
			setServiceAccounts(processedData);
		} catch (err: any) {
			setError(err.message || "Failed to fetch IAM accounts");
			toast({
				title: "Error",
				description: "Failed to fetch IAM accounts",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (projectId) {
			fetchIAMAccounts();
		}
	}, [projectId, datacenterId]);

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div className="flex items-center gap-3">
					<User className="h-6 w-6 text-blue-500" />
					<h2 className="text-xl font-semibold">Service Accounts and Roles</h2>
					{serviceAccounts.length > 0 && (
						<span className="px-2 py-1 bg-secondary rounded-full text-xs text-muted-foreground">
							{serviceAccounts.length} {serviceAccounts.length === 1 ? "account" : "accounts"}
						</span>
					)}
				</div>

				<Button onClick={() => fetchIAMAccounts()} variant="outline" size="sm" disabled={loading}>
					{loading ? (
						<div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					) : (
						<RefreshCw className="h-4 w-4 mr-2" />
					)}
					Refresh
				</Button>
			</div>

			{error && (
				<div className="bg-red-900/20 border border-red-800/30 text-red-400 p-4 rounded-md">
					{error}
				</div>
			)}

			{loading ? (
				<div className="flex justify-center items-center py-8">
					<div className="animate-spin h-6 w-6 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					<span>Loading service accounts...</span>
				</div>
			) : serviceAccounts.length > 0 ? (
				<div className="space-y-4">
					{serviceAccounts.map((account) => (
						<Collapsible
							key={account.email}
							open={expandedAccounts[account.email]}
							onOpenChange={() => toggleAccountExpansion(account.email)}
							className="w-full"
						>
							<Card
								className={`overflow-hidden border-border hover:border-blue-500/30 transition-colors ${
									account.isNextDR ? "border-blue-500/50 bg-blue-500/5" : ""
								}`}
							>
								<CardContent className="p-4">
									<CollapsibleTrigger className="w-full">
										<div className="flex items-center justify-between w-full">
											<div className="flex items-center gap-3">
												<Key
													className={`h-5 w-5 ${
														account.isNextDR ? "text-blue-500" : "text-muted-foreground"
													}`}
												/>
												<div className="flex flex-col items-start">
													<div className="flex items-center gap-2">
														<h3 className="font-medium text-sm">{account.email}</h3>
														{account.isNextDR && (
															<Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
																NextDR
															</Badge>
														)}
													</div>
													<span className="text-xs text-muted-foreground">
														{account.roles.length} {account.roles.length === 1 ? "role" : "roles"}
													</span>
												</div>
											</div>
											{expandedAccounts[account.email] ? (
												<ChevronDown className="h-4 w-4 ml-1 flex-shrink-0 text-muted-foreground" />
											) : (
												<ChevronRight className="h-4 w-4 ml-1 flex-shrink-0 text-muted-foreground" />
											)}
										</div>
									</CollapsibleTrigger>

									<CollapsibleContent className="mt-4 space-y-3 pt-3 border-t border-border">
										<div className="flex flex-col gap-2">
											<span className="text-xs font-medium text-muted-foreground">
												Assigned Roles:
											</span>
											<div className="flex flex-wrap gap-2">
												{account.roles.map((role) => (
													<Badge
														key={role}
														variant="outline"
														className="text-xs font-mono bg-secondary/50"
													>
														{role.split("/").pop()}
													</Badge>
												))}
											</div>
										</div>
									</CollapsibleContent>
								</CardContent>
							</Card>
						</Collapsible>
					))}
				</div>
			) : (
				<div className="text-center p-8 bg-secondary rounded-lg">
					<User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h4 className="text-lg font-medium mb-2">No service accounts found</h4>
					<p className="text-muted-foreground">This project has no service accounts configured</p>
				</div>
			)}
		</div>
	);
}
