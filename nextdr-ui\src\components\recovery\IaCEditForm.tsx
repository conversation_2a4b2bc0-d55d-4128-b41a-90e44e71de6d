import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";
import { useUpdateRecoveryStep, useAddRecoveryStep } from "@/lib/api/hooks/recoveryPlans";
import { Save } from "lucide-react";
import * as apiClient from "@/lib/api/api-client";
import { Calendar } from "lucide-react";

interface IaCEditFormProps {
	step: RecoveryStep;
	onSave: () => void;
	onCancel: () => void;
	apiConfig: Record<string, any>;
}

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	project_naming_option: z.enum(["unique", "same", "custom"]),
	custom_project_name: z.string().optional(),
	snapshot_point_id: z.string().optional(),
	selected_components: z.array(z.string()).min(1, "At least one component must be selected"),
});

type FormValues = z.infer<typeof formSchema>;

const availableComponents = [
	{ id: "project", name: "Project" },
	{ id: "network", name: "Network" },
	{ id: "subnet", name: "Subnet" },
	{ id: "firewall", name: "Firewall Rules" },
	{ id: "router", name: "Router" },
	{ id: "nat", name: "NAT Gateway" },
];

interface SnapshotPoint {
	id: string;
	name: string;
	timestamp: string;
}

const IaCEditForm: React.FC<IaCEditFormProps> = ({ step, onSave, onCancel, apiConfig }) => {
	const [saveInProgress, setSaveInProgress] = useState<boolean>(false);
	const [snapshotPoints, setSnapshotPoints] = useState<SnapshotPoint[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const updateStep = useUpdateRecoveryStep();
	const addStep = useAddRecoveryStep();

	const config = step.configuration as {
		project_naming?: string;
		components?: string[];
		project_naming_option?: "unique" | "same" | "custom";
		custom_project_name?: string;
		snapshot_point_id?: string;
		selected_components?: string[];
	};

	const projectNaming = config.project_naming_option || config.project_naming || "unique";
	const components = config.selected_components ||
		config.components || ["project", "network", "subnet"];
	const customProjectName = config.custom_project_name || "";
	const snapshotPointId = config.snapshot_point_id || "";

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		setValue,
		watch,
		reset,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: step.name || "",
			project_naming_option: projectNaming as "unique" | "same" | "custom",
			custom_project_name: customProjectName,
			snapshot_point_id: snapshotPointId,
			selected_components: components,
		},
	});

	const selectedProjectNaming = watch("project_naming_option");
	const selectedComponents = watch("selected_components");

	useEffect(() => {
		const fetchSnapshotPoints = async () => {
			setLoading(true);
			try {
				const config = step.configuration as {
					project_id?: string;
				};

				if (!config.project_id) {
					console.error("Missing project_id in step configuration");
					setLoading(false);
					return;
				}

				const snapshots = await apiClient.getSnapshots(
					apiConfig.project_id,
					apiConfig.datacenter_id
				);

				if (snapshots && snapshots.length > 0) {
					// Format snapshots as snapshot points
					const formattedSnapshots = snapshots.map((snapshot: any) => {
						// Extract timestamp from snapshot name if possible
						const timestampMatch = snapshot.name.match(/-(\d+)$/);
						const timestamp = timestampMatch
							? new Date(parseInt(timestampMatch[1])).toISOString()
							: snapshot.creationTimestamp || new Date().toISOString();

						return {
							id: snapshot.name,
							name: snapshot.description || `Snapshot from ${new Date(timestamp).toLocaleString()}`,
							timestamp: timestamp,
						};
					});

					setSnapshotPoints(formattedSnapshots);
				} else {
					setSnapshotPoints([]);
				}
			} catch (error) {
				console.error("Error fetching snapshot points:", error);
				toast.error("Failed to load snapshot points");
				setSnapshotPoints([]);
			} finally {
				setLoading(false);
			}
		};

		fetchSnapshotPoints();
	}, [step]);

	const formatTimestamp = (timestamp: string) => {
		if (!timestamp) return "N/A";
		const date = new Date(timestamp);
		return date.toLocaleString();
	};

	const toggleComponent = (componentId: string) => {
		// Get the current value directly from the form state
		const currentComponents = watch("selected_components");
		const isSelected = currentComponents.includes(componentId);

		// Update the form value
		if (isSelected) {
			// Remove the component if it's already selected
			setValue(
				"selected_components",
				currentComponents.filter((id) => id !== componentId),
				{ shouldValidate: true }
			);
		} else {
			// Add the component if it's not selected
			setValue("selected_components", [...currentComponents, componentId], {
				shouldValidate: true,
			});
		}
	};

	const onSubmit = async (data: FormValues) => {
		setSaveInProgress(true);
		try {
			// Get the existing configuration
			const existingConfig = step.configuration as {
				project_id?: string;
				datacenter_id?: string;
				zone?: string;
			};

			const stepConfiguration = {
				...step.configuration,
				type: "IaC",
				project_naming_option: data.project_naming_option,
				custom_project_name: data.custom_project_name,
				snapshot_point_id: data.snapshot_point_id,
				selected_components: data.selected_components,
				project_naming: data.project_naming_option,
				components: data.selected_components,
				// Preserve these important fields
				project_id: existingConfig.project_id,
				datacenter_id: existingConfig.datacenter_id,
				zone: existingConfig.zone,
			};

			// Check if this is a new step (no ID) or existing step
			const isNewStep = !step.id;

			if (isNewStep) {
				// For new steps, we need to create the step first
				if (!step.recovery_plan_id) {
					console.error("Missing recovery_plan_id for new step:", step);
					toast.error("Cannot save configuration: Missing recovery plan ID");
					return;
				}

				const newStepData: Omit<RecoveryStep, "id" | "created_at"> = {
					name: data.name || "IaC Step",
					operation_type: "IaC",
					recovery_plan_id: step.recovery_plan_id,
					step_order: step.step_order || 0,
					configuration: stepConfiguration,
				};

				console.log("Creating new IaC step:", newStepData);

				addStep.mutate(newStepData, {
					onSuccess: () => {
						toast.success("IaC step created successfully");
						onSave();
					},
					onError: (error) => {
						toast.error(`Failed to create step: ${error.message}`);
					},
				});
			} else {
				// For existing steps, update them
				const updatedStep: RecoveryStep = {
					...step,
					name: data.name,
					configuration: stepConfiguration,
				};

				console.log("Updating existing IaC step:", updatedStep);

				updateStep.mutate(updatedStep, {
					onSuccess: () => {
						toast.success("IaC configuration saved successfully");
						onSave();
					},
					onError: (error) => {
						toast.error(`Failed to save IaC configuration: ${error.message}`);
					},
				});
			}
		} catch (error: any) {
			console.error("Error saving IaC step:", error);
			toast.error("Failed to save IaC step");
		} finally {
			setSaveInProgress(false);
		}
	};

	return (
		<div className="rounded-lg bg-dr-dark">
			<div className="p-4 border-b border-dr-purple/5">
				<h3 className="text-lg font-semibold text-dr-purple/80 mb-1">Edit IaC Step: {step.name}</h3>
				<p className="text-dr-gray text-sm">
					Configure project naming, snapshot point, and components to recover.
				</p>
			</div>

			<form onSubmit={handleSubmit(onSubmit)} className="p-4 space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-2">
						<label htmlFor="name" className="block text-sm font-medium text-dr-purple/80">
							Step Name
						</label>
						<Input
							id="name"
							placeholder="Enter step name"
							{...register("name")}
							className="bg-dr-dark border-dr-purple/30 text-white placeholder-dr-gray focus:border-dr-purple focus:ring-dr-purple hover:border-dr-purple/60 transition-colors"
						/>
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label
							htmlFor="project_naming_option"
							className="block text-sm font-medium text-dr-purple/80"
						>
							Project Naming
						</label>
						<Select
							onValueChange={(value) =>
								setValue("project_naming_option", value as "unique" | "same" | "custom")
							}
							defaultValue={selectedProjectNaming}
						>
							<SelectTrigger className="bg-dr-dark border-dr-purple/30 text-white hover:border-dr-purple/60 transition-colors">
								<SelectValue placeholder="Select project naming option" />
							</SelectTrigger>
							<SelectContent className="bg-dr-dark border-dr-purple/5 text-white">
								<SelectItem value="unique">Generate unique name</SelectItem>
								<SelectItem value="same">Keep same name</SelectItem>
								<SelectItem value="custom">Custom name</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{selectedProjectNaming === "custom" && (
						<div className="space-y-2">
							<label
								htmlFor="custom_project_name"
								className="block text-sm font-medium text-dr-purple/80"
							>
								Custom Project Name
							</label>
							<Input
								id="custom_project_name"
								placeholder="Enter custom project name"
								{...register("custom_project_name")}
								className="bg-dr-dark border-dr-purple/30 text-white placeholder-dr-gray focus:border-dr-purple focus:ring-dr-purple hover:border-dr-purple/60 transition-colors"
							/>
						</div>
					)}

					<div className="space-y-2">
						<label
							htmlFor="snapshot_point_id"
							className="block text-sm font-medium text-dr-purple/80"
						>
							Snapshot Point
						</label>
						{loading ? (
							<div className="p-2 bg-dr-dark border border-dr-purple/5 rounded-md flex items-center justify-center">
								<div className="h-4 w-4 border-2 border-dr-gray border-t-transparent rounded-full animate-spin mr-2"></div>
								<span className="text-dr-gray">Loading snapshot points...</span>
							</div>
						) : (
							<Select
								onValueChange={(value) => setValue("snapshot_point_id", value)}
								defaultValue={snapshotPointId}
							>
								<SelectTrigger className="bg-dr-dark border-dr-purple/30 text-white hover:border-dr-purple/60 transition-colors">
									<SelectValue placeholder="Select snapshot point" />
								</SelectTrigger>
								<SelectContent className="bg-dr-dark border-dr-purple/5 text-white">
									{snapshotPoints.length > 0 ? (
										snapshotPoints.map((point) => (
											<SelectItem key={point.id} value={point.id}>
												<div className="flex items-center space-x-2">
													<Calendar className="h-4 w-4 text-dr-purple" />
													<span>{formatTimestamp(point.timestamp)}</span>
												</div>
											</SelectItem>
										))
									) : (
										<SelectItem value="no-snapshots" disabled>
											No snapshot points available
										</SelectItem>
									)}
								</SelectContent>
							</Select>
						)}
					</div>
				</div>

				<div className="space-y-2">
					<label className="block text-sm font-medium text-dr-purple/80 mb-2">
						Components to Recover
					</label>
					<div className="grid grid-cols-2 md:grid-cols-3 gap-2">
						{availableComponents.map((component) => {
							const isSelected = selectedComponents.includes(component.id);
							return (
								<div
									key={component.id}
									className={`p-2 rounded-md border cursor-pointer transition-colors ${
										isSelected
											? "border-dr-purple bg-dr-purple/10"
											: "border-dr-purple/5 bg-dr-dark hover:bg-dr-purple/5"
									}`}
									onClick={() => toggleComponent(component.id)}
								>
									<div className="flex items-center space-x-2">
										<Checkbox
											checked={isSelected}
											onCheckedChange={() => toggleComponent(component.id)}
											className="data-[state=checked]:bg-dr-purple"
										/>
										<span className="text-sm text-white">{component.name}</span>
									</div>
								</div>
							);
						})}
					</div>
					{errors.selected_components && (
						<p className="text-sm text-red-500">{errors.selected_components.message}</p>
					)}
				</div>

				<div className="flex justify-end space-x-3 pt-4 border-t border-dr-purple/5">
					<Button
						variant="outline"
						onClick={onCancel}
						type="button"
						className="border-dr-purple/5 hover:bg-dr-purple/5"
					>
						Cancel
					</Button>
					<Button
						type="submit"
						disabled={saveInProgress}
						className={`px-4 py-2 rounded-md flex items-center justify-center space-x-2 ${
							saveInProgress
								? "bg-dr-purple/20 text-dr-gray cursor-not-allowed"
								: "bg-dr-purple text-white hover:bg-dr-purple/90"
						}`}
					>
						{saveInProgress ? (
							<>
								<div className="h-4 w-4 border-2 border-dr-gray border-t-transparent rounded-full animate-spin"></div>
								<span>Saving...</span>
							</>
						) : (
							<>
								<Save className="h-5 w-5" />
								<span>Save Configuration</span>
							</>
						)}
					</Button>
				</div>
			</form>
		</div>
	);
};

export default IaCEditForm;
