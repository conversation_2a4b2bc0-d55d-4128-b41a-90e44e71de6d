# License Server

A Next.js application for managing software licenses with Supabase magic link authentication.

## Authentication System

This application uses a purely client-side magic link authentication system with Supabase. The auth flow is:

1. User enters email on login page
2. Magic link is sent to their email
3. User clicks the magic link
4. They are redirected back to the app and automatically logged in
5. Session is managed client-side with React Context

## Setting Up Admin Access

### First Time Setup

1. **Sign up/Login**: Go to `/login` and enter your email to receive a magic link
2. **Set Admin Role**: After logging in, you need to set your user role to "admin" in Supabase:

   **Option A: Using Supabase Dashboard**

   - Go to your Supabase project dashboard
   - Navigate to Authentication → Users
   - Find your user and click to edit
   - In "Raw User Meta Data", add: `{"role": "admin"}`
   - Save the changes

   **Option B: Using the Setup Page**

   - Visit `/setup` after logging in
   - Follow the instructions provided

3. **Access Dashboard**: Once admin role is set, you can access `/admin/dashboard`

### Adding More Admin Users

Use the provided script to set admin role for additional users:

```bash
# Set environment variables
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# Run the script
node scripts/set-admin-role.js <EMAIL>
```

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Key Features

- **Pure Client-Side Auth**: No server-side middleware interference
- **Magic Link Only**: Passwordless authentication
- **Role-Based Access**: Admin role required for dashboard access
- **Session Management**: Automatic session handling with React Context
- **Error Handling**: Graceful handling of auth errors and role permissions

## Troubleshooting

### Redirect Loop Issue

If you experience redirect loops, it's likely because:

1. The user doesn't have admin role set in their metadata
2. Visit `/setup` page for instructions on setting up admin access

### Magic Link Not Working

1. Check your email (including spam folder)
2. Ensure Supabase is configured correctly
3. Check browser console for any errors
