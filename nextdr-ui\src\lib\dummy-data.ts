import { ApplicationGroup, Datacenter, RecoveryPlan, VM } from "./types";

// Dummy datacenters
export const dummyDatacenters: Datacenter[] = [
	{
		id: "dc-001",
		name: "GCP US East",
		hypervisor_type: "GCP",
		project_id: "nextdr-demo-project",
		status: "connected",
		created_at: "2023-01-15T12:00:00Z",
	},
	{
		id: "dc-002",
		name: "AWS Production",
		hypervisor_type: "AWS",
		project_id: "aws-prod-account",
		status: "connected",
		created_at: "2023-02-20T14:30:00Z",
	},
	{
		id: "dc-003",
		name: "On-prem VMware",
		hypervisor_type: "VMware",
		status: "connected",
		created_at: "2023-03-10T09:15:00Z",
	},
	{
		id: "dc-004",
		name: "Dev Proxmox",
		hypervisor_type: "Proxmox",
		status: "error",
		created_at: "2023-04-05T16:45:00Z",
	},
];

// Dummy VMs
export const dummyVMs: VM[] = [
	{
		id: "vm-001",
		name: "web-server-01",
		vm_id: "gce-instance-1",
		datacenter_id: "dc-001",
		power_state: "running",
		cpu_count: 4,
		memory_size_mb: 8192,
		status: "healthy",
		os_type: "Linux",
		ip_address: "*********",
	},
	{
		id: "vm-002",
		name: "app-server-01",
		vm_id: "gce-instance-2",
		datacenter_id: "dc-001",
		power_state: "running",
		cpu_count: 8,
		memory_size_mb: 16384,
		status: "healthy",
		os_type: "Linux",
		ip_address: "*********",
	},
	{
		id: "vm-003",
		name: "db-server-01",
		vm_id: "i-07f94a98b76543e1c",
		datacenter_id: "dc-002",
		power_state: "running",
		cpu_count: 16,
		memory_size_mb: 32768,
		status: "healthy",
		os_type: "Linux",
		ip_address: "************",
	},
	{
		id: "vm-004",
		name: "cache-server-01",
		vm_id: "i-07f94a98b76543e1d",
		datacenter_id: "dc-002",
		power_state: "stopped",
		cpu_count: 4,
		memory_size_mb: 8192,
		status: "stopped",
		os_type: "Linux",
		ip_address: "************",
	},
	{
		id: "vm-005",
		name: "vm-esxi-01",
		vm_id: "vm-123",
		datacenter_id: "dc-003",
		power_state: "running",
		cpu_count: 2,
		memory_size_mb: 4096,
		status: "healthy",
		os_type: "Windows",
		ip_address: "*************",
	},
	{
		id: "vm-006",
		name: "vm-proxmox-01",
		vm_id: "100",
		datacenter_id: "dc-004",
		power_state: "running",
		cpu_count: 2,
		memory_size_mb: 2048,
		status: "warning",
		os_type: "Linux",
		ip_address: "*************",
	},
];

// Dummy Application Groups
export const dummyAppGroups: ApplicationGroup[] = [
	{
		id: "ag-001",
		name: "Web Tier",
		description: "Frontend web servers for customer portal",
		vm_ids: ["vm-001"],
		created_at: "2023-02-15T10:30:00Z",
		vm_count: 1,
		data_center_id: "nextdr-demo-project",
	},
	{
		id: "ag-002",
		name: "App Tier",
		description: "Application servers for business logic",
		vm_ids: ["vm-002"],
		created_at: "2023-02-15T10:35:00Z",
		vm_count: 1,
		data_center_id: "nextdr-demo-project",
	},
	{
		id: "ag-003",
		name: "Database Tier",
		description: "Database servers for persistent storage",
		vm_ids: ["vm-003"],
		created_at: "2023-02-15T10:40:00Z",
		vm_count: 1,
		data_center_id: "aws-prod-account",
	},
	{
		id: "ag-004",
		name: "Dev Environment",
		description: "Development environment resources",
		vm_ids: ["vm-005", "vm-006"],
		created_at: "2023-03-20T14:20:00Z",
		vm_count: 2,
		data_center_id: "nextdr-demo-project",
	},
];

// Dummy Recovery Plans
export const dummyRecoveryPlans: RecoveryPlan[] = [
	{
		id: "rp-001",
		name: "Web Tier Recovery",
		description: "Recovery plan for web tier",
		app_group_id: "ag-001",
		created_at: "2023-04-10T11:00:00Z",
		steps_count: 4,
		status: "ready",
	},
	{
		id: "rp-002",
		name: "Full Application Recovery",
		description: "Complete application stack recovery",
		app_group_id: "ag-002",
		created_at: "2023-04-15T09:30:00Z",
		steps_count: 6,
		status: "draft",
	},
	{
		id: "rp-003",
		name: "Database Recovery",
		description: "Database tier recovery with point-in-time restore",
		app_group_id: "ag-003",
		created_at: "2023-04-20T14:15:00Z",
		steps_count: 3,
		status: "ready",
	},
];

// Helper function to get VMs for a specific datacenter
export const getVMsByDatacenterId = (datacenterId: string) => {
	return dummyVMs.filter((vm) => vm.datacenter_id === datacenterId);
};

// Helper function to get VMs for a specific application group
export const getVMsByAppGroupId = (appGroupId: string) => {
	const appGroup = dummyAppGroups.find((ag) => ag.id === appGroupId);
	if (!appGroup) return [];
	return dummyVMs.filter((vm) => appGroup.vm_ids.includes(vm.id));
};

// Helper function to get application group by ID
export const getAppGroupById = (appGroupId: string) => {
	return dummyAppGroups.find((ag) => ag.id === appGroupId);
};
