import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useModalStore } from "@/lib/store/useStore";
import { ApplicationGroup } from "@/lib/types";
import { toast } from "@/components/ui/sonner";
import {
	useAddApplicationGroup,
	useUpdateApplicationGroup,
	useDatacenters,
} from "@/lib/api/dataFetching";

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	description: z.string().min(1, "Description is required"),
	datacenter_id: z.string().min(1, "Datacenter is required"),
});

type FormValues = z.infer<typeof formSchema>;

const ApplicationGroupModal = () => {
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "applicationGroup";
	const { data: datacenters = [], isLoading: isLoadingDatacenters } = useDatacenters();

	const isEditMode = !!modalData?.group;
	const groupToEdit = modalData?.group as ApplicationGroup | undefined;

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
		setValue,
		watch,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: groupToEdit?.name || "",
			description: groupToEdit?.description || "",
			datacenter_id: groupToEdit?.data_center_id || "",
		},
	});

	React.useEffect(() => {
		if (showModal) {
			if (isEditMode && groupToEdit) {
				setValue("name", groupToEdit.name);
				setValue("description", groupToEdit.description || "");
				setValue("datacenter_id", groupToEdit.data_center_id);
			} else if (!isEditMode) {
				// Reset form when opening for a new group
				reset({
					name: "",
					description: "",
					datacenter_id: "",
				});
			}
		}

		return () => {
			reset();
		};
	}, [showModal, isEditMode, groupToEdit, setValue, reset]);

	const selectedDatacenterId = watch("datacenter_id");
	const addApplicationGroup = useAddApplicationGroup();
	const updateApplicationGroup = useUpdateApplicationGroup();

	const onSubmit = async (data: FormValues) => {
		try {
			if (isEditMode && groupToEdit) {
				const updatedGroup: ApplicationGroup = {
					...groupToEdit,
					name: data.name,
					description: data.description,
					data_center_id: data.datacenter_id,
				};

				updateApplicationGroup.mutate(updatedGroup);
			} else {
				const newApplicationGroup: Partial<ApplicationGroup> = {
					name: data.name,
					description: data.description,
					vm_ids: [],
					data_center_id: data.datacenter_id,
				};

				addApplicationGroup.mutate(newApplicationGroup as any);
			}

			reset();
			onClose();
		} catch (error) {
			console.error(`Error ${isEditMode ? "updating" : "creating"} application group:`, error);
			toast.error(`Failed to ${isEditMode ? "update" : "create"} application group`);
		}
	};

	return (
		<Dialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						{isEditMode ? "Edit Application Group" : "New Application Group"}
					</DialogTitle>
					<DialogDescription>
						{isEditMode
							? "Update your application group details."
							: "Create a new application group to organize your VMs for protection."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="name" className="text-sm font-medium">
							Name
						</label>
						<Input id="name" placeholder="Enter application group name" {...register("name")} />
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label htmlFor="description" className="text-sm font-medium">
							Description
						</label>
						<Textarea
							id="description"
							placeholder="Enter description"
							{...register("description")}
						/>
						{errors.description && (
							<p className="text-sm text-red-500">{errors.description.message}</p>
						)}
					</div>

					<div className="space-y-2">
						<label htmlFor="datacenter_id" className="text-sm font-medium">
							Datacenter
						</label>
						<Select
							onValueChange={(value) => setValue("datacenter_id", value)}
							value={selectedDatacenterId}
							defaultValue={selectedDatacenterId}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select datacenter" />
							</SelectTrigger>
							<SelectContent>
								{isLoadingDatacenters ? (
									<SelectItem value="loading" disabled>
										Loading datacenters...
									</SelectItem>
								) : datacenters.length > 0 ? (
									datacenters.map((dc) => (
										<SelectItem key={dc.id} value={dc.project_id || dc.id.toString()}>
											{dc.project_id} ({dc.hypervisor_type})
										</SelectItem>
									))
								) : (
									<SelectItem value="none" disabled>
										No datacenters available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
						{errors.datacenter_id && (
							<p className="text-sm text-red-500">{errors.datacenter_id.message}</p>
						)}
					</div>

					<DialogFooter>
						<Button variant="outline" type="button" onClick={onClose}>
							Cancel
						</Button>
						<Button
							type="submit"
							className="bg-dr-purple hover:bg-dr-purple-dark"
							disabled={isSubmitting}
						>
							{isSubmitting
								? isEditMode
									? "Updating..."
									: "Creating..."
								: isEditMode
								? "Update Group"
								: "Create Group"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default ApplicationGroupModal;
