#!/bin/bash
# ndrSupabase.sh - A Bash module providing common Supabase related functions

source "$(dirname "${BASH_SOURCE[0]}")/ndrDocker.sh"

# global variables
gMIGRATION_TIMESTAMP=""
gSUPABASE_MIGRATION_DIR="./supabase/migrations"
gNDR_SQL_SCHEMA_FILENAME="NDR-schema.sql"
gNDR_SUPABASE_DOCKER_COMPOSE_FILE="NDR-docker-compose.yml"
gSUPABASE_COMPOSE_COPY_MODE=0
gACTIVE_NDR_SQL_SCHEMA_FILE=""
gSUPABASE_REMOTE_PASSWORD=""
gSUPABASE_CLEANUP_RETAIN_IMAGES=true

# supabase command variables
gSUPABASE_CLI_CMD="sudo npx supabase"
gSUPABASE_NEXTDR_SUB="supabase-NDR_DB"
gSUPABASE_NEXTDR_HOME=""
gSUPABASE_TEMPLATE_SUB="supabase"
gSUPABASE_TEMPLATE_HOME=""
gSUPABASE_REMOTE_TOKEN="********************************************" # Current NextDR Production remote DB token. Should never change but edit if needed.
gSUPABASE_REMOTE_PROJECT_REFERENCE="utcestwwfefexcnmjcjr" #Prod DB
#gSUPABASE_REMOTE_PASSWORD="@nextdr!123" #Prod DB new human readable

NDR_SUPABASE_APP_SERVICE_NAME_DB="db"
NDR_SUPABASE_APP_SERVICE_NAME_VECTOR="vector"

# supabase cleanup mode options
export NDR_SUPABASE_CLEANUP_OPTION_STRICT=0x1 # fail on any error, default behavior for standard uninstalls.
export NDR_SUPABASE_CLEANUP_OPTION_BEST_EFFORT=0x1 # in prepare phase, we do a best effort cleanup and dont fail on errors.
export NDR_SUPABASE_CLEANUP_OPTION_DEFAULT=$(( NDR_SUPABASE_CLEANUP_OPTION_STRICT ))

# supabase application build enum
export NDR_SUPABASE_APP_BUILD_OPTIONS_NONE=0x0

# prepare phase options
export NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PREREQUISITES=0x1 # install and devops
export NDR_SUPABASE_APP_BUILD_OPTIONS_HOME_DIR_CHECK=0x2        # (install only, devops will always use the current working directory since all will be cleaned up at the end)
export NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_EXISTING_APP=0x4  # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_PREPARE_PHASE=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PREREQUISITES | \
                                                        NDR_SUPABASE_APP_BUILD_OPTIONS_HOME_DIR_CHECK | \
                                                        NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_EXISTING_APP ))

# configure phase options
export NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_DIRS=0x10     # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_GIT_CLONE_TEMPLATE=0x20       # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_COMPOSE_FILES=0x40       # devops and install, template files only.
export NDR_SUPABASE_APP_BUILD_OPTIONS_CUSTOMIZE_COMPOSE_FILE=0x80   # devops and install, note- Install will copy precustomized file from installer archive.
export NDR_SUPABASE_APP_BUILD_OPTIONS_QUERY_COMPOSE_SERVICES=0x100  # install and devops
export NDR_SUPABASE_APP_BUILD_OPTIONS_CONFIGURE_PHASE=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_DIRS | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_GIT_CLONE_TEMPLATE | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_COMPOSE_FILES | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_CUSTOMIZE_COMPOSE_FILE | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_QUERY_COMPOSE_SERVICES ))

# build/install phase options
export NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_APP_REGISTRY=0x1000 # install only
export NDR_SUPABASE_APP_BUILD_OPTIONS_SCHEMA_FILE_CHECK=0x2000            # devops, note- install will pull the necessary image with schema baked in
export NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_SCHEMA_FILE=0x4000             # devops, note- install will pull the necessary image with schema baked in
export NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_ENV_FILE=0x8000                # devops and install, note- install will package the env file with the installer archive
export NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_SUPABASE_BASE_IMAGES=0x10000   # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_NDR_SUPABASE_IMAGES=0x20000    # install only
export NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_BRIDGE_NETWORK=0x40000       # devops and install, note- not required by devops build, but docker compose command will fail since the entry is in the compose file.
export NDR_SUPABASE_APP_BUILD_OPTIONS_START_CONTAINERS=0x80000            # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_VERIFY_CONTAINERS=0x100000          # devops and install
export NDR_SUPABASE_APP_BUILD_OPTIONS_APP_INSTALL_COMPLETE_NOTIFY=0x200000 # install only
export NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PHASE=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_APP_REGISTRY | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_SCHEMA_FILE_CHECK | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_SCHEMA_FILE | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_ENV_FILE | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_SUPABASE_BASE_IMAGES | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_NDR_SUPABASE_IMAGES | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_BRIDGE_NETWORK | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_START_CONTAINERS | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_VERIFY_CONTAINERS | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_APP_INSTALL_COMPLETE_NOTIFY ))

# Post install phase options
export NDR_SUPABASE_APP_BUILD_OPTIONS_STOP_CONTAINERS=0x1000000             # devops only
export NDR_SUPABASE_APP_BUILD_OPTIONS_UPLOAD_NDR_SUPABASE_IMAGES=0x2000000  # devops only
export NDR_SUPABASE_APP_BUILD_OPTIONS_REMOVE_SUPABASE_APP=0x4000000         # devops only
export NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_SUPABASE_DIRS=0x8000000       # devops and install, note- devops removes all folders, install only removes the template folder, note2- install mode essentially ends here
export NDR_SUPABASE_APP_BUILD_OPTIONS_POST_INSTALL_PHASE=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_STOP_CONTAINERS | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_UPLOAD_NDR_SUPABASE_IMAGES | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_REMOVE_SUPABASE_APP | \
                                                          NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_SUPABASE_DIRS ))

# mode
export NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_MODE=0x10000000
export NDR_SUPABASE_APP_BUILD_OPTIONS_DEVOPS_MODE=0x20000000


export NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_MODE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PREREQUISITES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_HOME_DIR_CHECK | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_SCHEMA_FILE_CHECK | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_EXISTING_APP | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_DIRS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_GIT_CLONE_TEMPLATE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_COMPOSE_FILES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CUSTOMIZE_COMPOSE_FILE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_QUERY_COMPOSE_SERVICES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_APP_REGISTRY | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_ENV_FILE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_SUPABASE_BASE_IMAGES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_NDR_SUPABASE_IMAGES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_BRIDGE_NETWORK | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_START_CONTAINERS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_VERIFY_CONTAINERS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_APP_INSTALL_COMPLETE_NOTIFY | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_SUPABASE_DIRS ))

export NDR_SUPABASE_APP_BUILD_OPTIONS_DEVOPS=$(( NDR_SUPABASE_APP_BUILD_OPTIONS_DEVOPS_MODE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PREREQUISITES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_SCHEMA_FILE_CHECK | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_EXISTING_APP | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_DIRS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_GIT_CLONE_TEMPLATE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_COMPOSE_FILES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CUSTOMIZE_COMPOSE_FILE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_QUERY_COMPOSE_SERVICES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_SCHEMA_FILE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_ENV_FILE | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_SUPABASE_BASE_IMAGES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_BRIDGE_NETWORK | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_START_CONTAINERS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_VERIFY_CONTAINERS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_SUPABASE_DIRS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_STOP_CONTAINERS | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_UPLOAD_NDR_SUPABASE_IMAGES | \
                                                  NDR_SUPABASE_APP_BUILD_OPTIONS_REMOVE_SUPABASE_APP ))

# --- FUNCTIONS ---

function ndr_SupabasePullDatabaseReferenceSchemaFile ()
{
  local logSectionDesc="Executing supabase DB pull"
  ndr_logSecStart "$logSectionDesc"

  cd "$gNEXTDR_HOME_DIR" || { ndr_logError "Failed to cd into home dir"; return 1; }

  # pre check for any existing migrations. If any exist, the DB pull may fail.
  # If this is the first time running this script, there should be no migrations present.
  # If there are migrations present, the user will need to run the migration repair command to remove the entry from the remote migration history table.
  # Set the directory to query
  
  
  # Check if the directory exists. Directory will not exist for fresh installs so skip check if not present.
  if [ ! -d "$gSUPABASE_MIGRATION_DIR" ]; then
    ndr_logInfo "Directory $gSUPABASE_MIGRATION_DIR does not exist, skipping precheck. "
    #return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$gSUPABASE_MIGRATION_DIR"/*)
  COUNT=0
  
  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ndr_logInfo "Removing existing migration file: [$FILE]"
      # delete original migration file from migrations folder
      rm "$FILE"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove preexisting migration file [$FILE]. Please remove manually."
        #return 1
      fi
      
      # Extract just the filename portion (strip path)
      BASENAME=$(basename "$FILE")

      # Check if filename contains an underscore
      if [[ "$BASENAME" != *_* ]]; then
        ndr_logWarn "Invalid filename format. No underscore found in [$BASENAME], skipping."
        continue
      fi

      # Extract the timestamp value before the first underscore
      gMIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

      if [ -z "$gMIGRATION_TIMESTAMP" ]; then
        ndr_logWarn "Failed to extract migration timestamp from filename [$BASENAME], skipping. "
        continue
      fi

      ndr_logInfo "Using migration timestamp: [$gMIGRATION_TIMESTAMP]"

      # run migration entry reset against remote db with the timestamp
      # this will remove the entry from the remote migration history table
      cmd="$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP"
      if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
        cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
      fi    
      if [[ "$gDebugMode" -eq 1 ]]; then
        cmd="$cmd --debug"
      fi
      
      max_retries=3
      attempt=1

      while true; do
        eval "$cmd"
        
        return_code=$?

        if [[ $return_code -eq 0 ]]; then
          # success
          break
        fi

        if [[ $attempt -ge $max_retries ]]; then
          ndr_logError "Max supabase command retries reached. Exiting. "
          return 1
        fi

        ndr_logWarn "Supabase command attempt $attempt failed, retrying."
        
        
        attempt=$((attempt + 1))
        sleep 1  # Optional: wait before retrying
      done

      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove migration entry [$gMIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP]"
        #return 1
      fi

      ndr_logInfo "Removing remote migration entry for timestamp: [$gMIGRATION_TIMESTAMP]"
      
      ((COUNT++))
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logInfo "No preexisting migration files found in directory. Proceeding with db pull."
  else
    ndr_logInfo "Removed [$COUNT] migration files from dir [$gSUPABASE_MIGRATION_DIR]"
  fi

  # The auth and storage schemas are excluded by default. Run supabase db pull --schema auth,storage again to diff them.
  max_retries=3
  attempt=1

  cmd="$gSUPABASE_CLI_CMD db pull --linked"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  while true; do
    eval "$cmd" <<< "n"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase db pull failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# To install supabase CLI:
# https://supabase.com/docs/guides/local-development
function ndr_SupabaseCLIInstall ()
{
  local logSectionDesc="Installing supabase CLI"
  ndr_logSecStart "$logSectionDesc"

  cd "$gNEXTDR_HOME_DIR"  || { ndr_logError "Failed to cd into home dir"; return 1; }
  
  # check if supabase cli is already installed
  $gSUPABASE_CLI_CMD -v
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "Supabase CLI already installed"
    return 0
  fi

  npm install supabase --save-dev
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to install supabase CLI."
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"
}

# To begin linking to the remote instance
# eg. supabase link --project-ref <project-id> -password <password>
# You can get <project-id> from your project's dashboard URL: https://supabase.com/dashboard/project/<project-id>
# Alternatively, omitting the project ID will cause supabase to show all available remote db's to choose from.
# password is the master project password (not the supabase email account login)

function ndr_SupabaseLinkRemote ()
{
  local logSectionDesc="Executing supabase remote login"
  ndr_logSecStart "$logSectionDesc"

  # perform a remote logout in case the user is already logged in.
  supabaseLogoutRemote

  cmd="$gSUPABASE_CLI_CMD login --token $gSUPABASE_REMOTE_TOKEN"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase login with token failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Executing supabase init"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD init --force"
  $cmd <<< "n"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase init failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Linking local supabase project to remote master"
  ndr_logSecStart "$logSectionDesc"

  # There seems to be a bug in the supbase CLI where the link command (And others) still prompts for password even when the initial login with token was successful.
  # This results in several redundant prompts for password from a number of CLI commands.
  # As a workaround, lets prompt for the password interactively here and then pass it to the link command. If link command fails, the password was incorrect and retry the prompt up to 3 times.

  cmd="$gSUPABASE_CLI_CMD link --project-ref $gSUPABASE_REMOTE_PROJECT_REFERENCE"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do

    if [[ -z "$gSUPABASE_REMOTE_PASSWORD" ]]; then
      read -s -p "Enter the Supabase remote project password: " gSUPABASE_REMOTE_PASSWORD
      echo  # Print a newline after the password input
      
      if [[ -z "$gSUPABASE_REMOTE_PASSWORD" ]]; then
        ndr_logError "Supabase remote project password is required."
        continue
      else 
        cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
      fi
    fi

    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    # command failure could be due to an incoorect password so clear that to trigger a re-prompt.
    gSUPABASE_REMOTE_PASSWORD=""
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase link failed."
    # immediately run a supabase unlink command to remove the local link and clean up.
    $gSUPABASE_CLI_CMD unlink
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Capture any changes that you have made to your remote database before you went through in the process of pulling the schema.
# may prompt to update remote migration history table. select default.
# this process is lengthy and can take several minutes.

# If successful, Will output schema sql file to a folder stated in CLI output.
# supabase/migrations is now populated with a migration in <timestamp>_remote_schema.sql.
# eg. "Schema written to supabase\migrations\20250421192748_remote_schema.sql"

# query the output folder for the generated sql file. This will be under supabase/migrations.
# copy this file to the root project folder and delete the original from the migrations folder.
# parse the timestamp from the file name.
# Run the migration entry reset against the remote db with the timestamp. This will remove the entry from the remote migration history table.
# eg.  npx supabase migration repair --status reverted 20250422185759
function ndr_SupabaseProcessRemoteMigration ()
{
  local logSectionDesc="Processing supabase remote migration file"
  ndr_logSecStart "$logSectionDesc"

  # Set the directory to query
  DIR="./supabase/migrations"
  
  # Check if the directory exists
  if [ ! -d "$DIR" ]; then
    ndr_logError "Directory $DIR does not exist. "
    return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$DIR"/*)
  COUNT=0
  MIGRATION_FILENAME=""

  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ((COUNT++))
      MIGRATION_FILENAME="$FILE"
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logError "No migration files found in directory. "
    return 1
  elif [ "$COUNT" -gt 1 ]; then
    ndr_logError "Too many files migration found in directory. "
    return 1
  else
    ndr_logInfo "Found migration file: [$MIGRATION_FILENAME]"
    # You can now use $MIGRATION_FILENAME as needed
  fi

  # Extract just the filename portion (strip path)
  BASENAME=$(basename "$MIGRATION_FILENAME")

  # Extract the timestamp value before the first underscore
  gMIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

  ndr_logInfo "Using migration timestamp: [$gMIGRATION_TIMESTAMP]"

  #destMigrationFile=$gMIGRATION_TIMESTAMP"_"$gNDR_SQL_SCHEMA_FILENAME
  destMigrationFile=$gNDR_SQL_SCHEMA_FILENAME
  # check for existing migration refrence file in root project folder.
  # if one exists, backup/rename with timestamp appended.
  if [[ -f "$destMigrationFile" ]]; then
    # Get the UNIX timestamp (modification time) of the file
    timestamp=$(stat -c %Y "$destMigrationFile")

    # Convert timestamp to readable format: YYYYMMDD_HHMMSS
    readable_time=$(date -d @"$timestamp" +"%Y%m%d_%H%M%S")
    
    # Build the new filename with timestamp appended
    filename_base="${destMigrationFile%.*}"
    filename_ext="${destMigrationFile##*.}"

    if [[ "$filename_base" -eq "$destMigrationFile" ]]; then
      # No extension case
      backup_filename="${filename_base}_${readable_time}"
    else
      backup_filename="${filename_base}_${readable_time}.${filename_ext}"
    fi

    # Move the file to the new filename
    mv "$destMigrationFile" "$backup_filename"

    ndr_logInfo "Backing up preexisting $gCOMPANY_NAME SQL schema file [$destMigrationFile] to [$backup_filename]"
  fi

  # copy migration file to root project folder
  cp -f "$MIGRATION_FILENAME" "$gNEXTDR_HOME_DIR/$destMigrationFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy migration file [$MIGRATION_FILENAME]"
    return 1
  fi
  ndr_logInfo "Copied migration file [$MIGRATION_FILENAME] to [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # cache copied migration file name for later use in docker container construction
  gACTIVE_NDR_SQL_SCHEMA_FILE=$gNEXTDR_HOME_DIR/$destMigrationFile
  ndr_logInfo "Caching local $gCOMPANY_NAME SQL schema file for use in docker container construction [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # delete original migration file from migrations folder
  rm "$MIGRATION_FILENAME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove original migration file [$MIGRATION_FILENAME]. Please remove manually."
    #return 1
  fi

  # run migration entry reset against remote db with the timestamp
  # this will remove the entry from the remote migration history table
  cmd="$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi    
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do
    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove migration entry [$gMIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP]"
    #return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_SupabaseLogoutRemote ()
{
  local logSectionDesc="Unlinking local supabase project"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD unlink"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase unlink failed."
    #return 1
  fi

  # clean up a supabase folder if it exists
  supabaseCLILocalConfigDir="$gSCRIPT_HOME_DIR/supabase"
  if [ -d "$supabaseCLILocalConfigDir" ]; then
    ndr_logInfo "Removing any existing Supabase CLI init folder [$supabaseCLILocalConfigDir]"
    sudo rm --recursive --force "$supabaseCLILocalConfigDir"
  fi

  ndr_logSecEnd "$logSectionDesc"

  
  local logSectionDesc="Executing supabase remote logout"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD logout"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd <<< "y"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase logout failed."
    #return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# there are 2 types of objects that need to be removed:
# 1. the supabase container and its data. This can be done with the docker file and compose commands if present. Otherwise we can remove images and containers by name.
# 2. the supabase template and project directories and files.
function ndr_SupabaseContainerCleanup ()
{
  # Cleanup code snippet taken from "projectDir/reset.sh"
  # clean up supbase container
  local logSectionDesc="Cleaning up Supabase Container"
  ndr_logSecStart "$logSectionDesc"
  
  local cleanupOptions="${1:$NDR_SUPABASE_CLEANUP_OPTION_DEFAULT}"
  
  if [[ -z "$gNEXTDR_HOME_DIR" ]]; then
    # read from registry if package already present.
    local homeDir=""
    homeDir=$(ndr_getHomeDirReg)
    return_code=$?
    if [[ $return_code -eq 0 && -n "$homeDir" ]]; then
      ndr_logInfo "Found existing directory [$homeDir] in registry."
      gNEXTDR_HOME_DIR="$homeDir"
    fi
  fi
  
  if [[ -n "$gNEXTDR_HOME_DIR" ]]; then
    gSUPABASE_NEXTDR_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_NEXTDR_SUB}"
    gSUPABASE_TEMPLATE_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_TEMPLATE_SUB}"
  fi

  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  if [[ ! -d "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logInfo "Supabase project directory [$gSUPABASE_NEXTDR_HOME] does not exist. Nothing to clean up."
    return 0
  fi

  local dockerComposeFile="$gSUPABASE_NEXTDR_HOME/docker-compose.yml"
  local dockerComposeDevFile="$gSUPABASE_NEXTDR_HOME/dev/docker-compose.dev.yml"
  
  # step 1: stop and remove the supabase docker containers and images
  ndr_logInfo "Stopping and removing Supabase Docker container..."

  # if the home dir and compose file exists, we can query it for the service list and execute the compose down command.
  if [[ -f "$dockerComposeFile" ]]; then

    cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }

    if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
      ndr_ParseComposeFileServices "$dockerComposeFile"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to parse Docker compose file [$dockerComposeFile] service entries."
        #return 1
      fi
    fi

    docker compose -p "ndr-supabase" -f "$dockerComposeFile" -f "$dockerComposeDevFile" down -v --remove-orphans
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logWarn "Failed to stop and remove Supabase Docker container"
      if (( cleanupOptions & NDR_SUPABASE_CLEANUP_OPTION_DEFAULT )); then
        return 1
      fi
    fi
    ndr_logInfo "Stopped and removed Supabase Docker container"

  else
    ndr_logWarn "Docker compose file [$dockerComposeFile] not found, skipping compose down command."
  fi
  
  # some modes will have registry access (install), others will only have compose file access (devops)
  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    if [[ "$gNDR_INSTALL_MODE" == true ]]; then
      ndr_RegistryReadServiceEntries
      return_code=$?
      if [[ $return_code -ne 0 ]]; then
        ndr_logError "Failed to read service entries from registry."
        #return 1
      fi
    fi
  fi  

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    ndr_logError "No service entries found in compose or registry to act on."
    return 0
  fi

    
  imageFailed=0

  for service in "${ndr_supabase_container_services[@]}"; do
    if [[ "$gSUPABASE_CLEANUP_RETAIN_IMAGES" == true ]]; then
      ndr_logInfo "Global flag to retain Supabase images is set, skipping delete of [$service]"
      continue;
    fi

    #local container_name="${ndr_supabase_container_service_container_names[$service]}"
    local image_name="${ndr_supabase_container_service_image_names[$service]}"
    local image_tag="${ndr_supabase_container_service_image_tags[$service]}"

    local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    local dockerImage="$image_name:$image_tag"

    # todo
    ndr_cleanupDockerImage "$dockerImage" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove Docker image [$dockerImage]."
      imageFailed=1
      continue
    fi
  done

  if [ $imageFailed -eq 1 ]; then
    ndr_logError "One or more Supabase Docker images were not removed. Please check the logs for details."
    #return 1
  else
    ndr_logInfo "All Supabase Docker images removed."
  fi
  
  # remove bridge network
  if ! docker network ls --format '{{.Name}}' | grep -q "^${NDR_DOCKER_BRIDGE_NETWORK_NAME}$"; then
    ndr_Info "Docker network not found [$NDR_DOCKER_BRIDGE_NETWORK_NAME]."
  else
    output=$(docker network rm "$NDR_DOCKER_BRIDGE_NETWORK_NAME" 2>&1)
    return_code=$?
    if [ $return_code != 0 ]; then
      if echo "$output" | grep -q "network .* is in use"; then
        ndr_logWarn "Cannot remove Docker network '$NDR_DOCKER_BRIDGE_NETWORK_NAME': It is currently in use by one or more containers."
      else
        ndr_logWarn "Failed to remove Docker network [$NDR_DOCKER_BRIDGE_NETWORK_NAME]."
      fi
    else
      ndr_logInfo "Removed Docker network [$NDR_DOCKER_BRIDGE_NETWORK_NAME]."
    fi
  fi

  # step 2: remove all directories and files related to the supabase project
  ndr_logInfo "Removing Supabase project directories and files..."

  ndr_logInfo "Cleaning up bind-mounted directories."
  BIND_MOUNTS=(
    "$gSUPABASE_NEXTDR_HOME/volumes/db/data"
  )

  for DIR in "${BIND_MOUNTS[@]}"; do
    if [ -d "$DIR" ]; then
      ndr_logInfo "Deleting $DIR..."
      rm -rf "$DIR"
    else
      ndr_logInfo "Directory $DIR does not exist. Skipping bind mount deletion."
    fi
  done

  ndr_logInfo "Removing Supabase project directory [$gSUPABASE_NEXTDR_HOME] and template directory [$gSUPABASE_TEMPLATE_HOME]..."
  if [ -d "$gSUPABASE_NEXTDR_HOME" ]; then
    rm --recursive --force "$gSUPABASE_NEXTDR_HOME"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove Supabase project directory [$gSUPABASE_NEXTDR_HOME]. Please check permissions and try again."
      #return 1
    fi
  else
    ndr_logInfo "Supabase project directory [$gSUPABASE_NEXTDR_HOME] does not exist."
  fi

  if [ -d "$gSUPABASE_TEMPLATE_HOME" ]; then
    rm --recursive --force "$gSUPABASE_TEMPLATE_HOME"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove Supabase template directory [$gSUPABASE_TEMPLATE_HOME]. Please check permissions and try again."
      #return 1
    fi
  else
    ndr_logInfo "Supabase template directory [$gSUPABASE_TEMPLATE_HOME] does not exist."
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_SupabaseLocalSchemaFileCheck ()
{
  local logSectionDesc="Local $gCOMPANY_NAME schema file check"
  ndr_logSecStart "$logSectionDesc"
  
  #if [ "$gExpressMode" -eq 1 ]; then
  #  ndr_logInfo "Skipping local $gCOMPANY_NAME schema file check in express mode."
  #  return 0
  #fi

  # check for local NDR schema file. 
  # first check if the file is present in the home directory.
  # If not in the home folder, check if the file is present in the same folder as the script.

  # If not present, prompt user to exit and run the supabase db pull command to generate the file.
  if [ -z "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    # active schema file variable not populated. populate and recheck
    if [ -f "${gNEXTDR_HOME_DIR}/${gNDR_SQL_SCHEMA_FILENAME}" ]; then
      gACTIVE_NDR_SQL_SCHEMA_FILE="${gNEXTDR_HOME_DIR}/${gNDR_SQL_SCHEMA_FILENAME}"
      ndr_logInfo "Found local $gCOMPANY_NAME schema file in home directory [$gACTIVE_NDR_SQL_SCHEMA_FILE]"
    elif [ -f "${PWD}/${gNDR_SQL_SCHEMA_FILENAME}" ]; then
      gACTIVE_NDR_SQL_SCHEMA_FILE="$PWD/$gNDR_SQL_SCHEMA_FILENAME"
      ndr_logInfo "Found local $gCOMPANY_NAME schema file in script folder [$gACTIVE_NDR_SQL_SCHEMA_FILE]"
    else
      ndr_logError "Local $gCOMPANY_NAME schema file not found in home directory or script folder."
    fi
  fi
  
  if [ ! -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    ndr_logWarn "WARNING: Local copy of $gCOMPANY_NAME schema file [$gNDR_SQL_SCHEMA_FILENAME] not found."
    ndr_logInfo "This file is required to seed the Supabase Docker container with the $gCOMPANY_NAME schema upon startup and, without it, the container DB would be empty and require manual injection of the SQL schema after installation."
    ndr_logInfo "Returning to the main menu to generate or supply the file."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_customizeSupabaseDockerComposeFile () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logInfo "No build options specified."
    #return 1
  fi

  # install will additionally package the compose file with the installer archive so overwrite that one here.
  if [[ "$gNDR_INSTALL_MODE" == true ]]; then
    # Copy the packaged NextDR specific docker compose file to the project.
    local srcFile="$gSCRIPT_HOME_DIR/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE"
    local destFile="$gSUPABASE_NEXTDR_HOME/docker-compose.yml"
    cp -f $srcFile $destFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Docker compose file [$srcFile] to [$destFile]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Docker compose file [$srcFile] to [$destFile]"

    #return 0
  fi

  ndr_customizeSupabaseDockerComposeFileAddLogflareInfo
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add Logflare info."
    return 1
  fi
  ndr_logInfo "Successfully customized $gCOMPANY_NAME supabase docker compose file to add Logflare info."

  # customize the docker compose file to add the bridge network
  ndr_customizeSupabaseDockerComposeFileAddBridgeNetwork
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add bridge network."
    return 1
  fi
  ndr_logInfo "Successfully customized $gCOMPANY_NAME supabase docker compose file to add bridge network."
  
  if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
    # customize the docker compose file to add the schema file
    ndr_customizeSupabaseDockerComposeFileAddSchema
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to customize supabase docker compose file to add schema file."
      return 1
    fi
    ndr_logInfo "Successfully customized $gCOMPANY_NAME supabase docker compose file to add schema file."
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_customizeSupabaseDockerComposeFileAddLogflareInfo () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file to add logflare info"
  ndr_logSecStart "$logSectionDesc"

  local COMPOSE_FILE="${gSUPABASE_NEXTDR_HOME}/docker-compose.yml"
  
  if [[ ! -f $COMPOSE_FILE ]]; then
    ndr_logError "Docker compose file [$COMPOSE_FILE] not found."
    return 1
  fi

  # Get list of services
  mapfile -t services < <(yq eval '.services | keys | .[]' "$COMPOSE_FILE")
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to retrieve services from compose file [$COMPOSE_FILE]."
    return 1
  fi

  if [[ ${#services[@]} -eq 0 ]]; then
    echo "No services found in compose file."
    return 1
  fi

  service_name=$NDR_SUPABASE_APP_SERVICE_NAME_VECTOR

  #yq eval 'has("services") and .services | has("vector")' $COMPOSE_FILE | grep -q true
  result=$(yq eval 'has("services") and .services | has("vector")' $COMPOSE_FILE)
  #return_code=$?
  #if [ $return_code -ne 0 ]; then
  if [ "$result" != "true" ]; then
    ndr_logError "Compose file missing .services.vector section."
    return 1
  fi

  yq eval -i ".services.\"$service_name\".environment.LOGFLARE_PUBLIC_ACCESS_TOKEN = \"\${LOGFLARE_PUBLIC_ACCESS_TOKEN}\"" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add logflare public access token."
    return 1
  fi
  ndr_logInfo "Added logflare public access token to compose file."

  yq eval -i ".services.\"$service_name\".environment.LOGFLARE_PRIVATE_ACCESS_TOKEN = \"\${LOGFLARE_PRIVATE_ACCESS_TOKEN}\"" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add logflare private access token."
    return 1
  fi
  ndr_logInfo "Added logflare private access token to compose file."

  ndr_logInfo "Logflare info updated successfully in compose file [$COMPOSE_FILE]."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_customizeSupabaseDockerComposeFileAddBridgeNetwork () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file to add bridge network"
  ndr_logSecStart "$logSectionDesc"

  local COMPOSE_FILE="${gSUPABASE_NEXTDR_HOME}/docker-compose.yml"
  
  if [[ ! -f $COMPOSE_FILE ]]; then
    ndr_logError "Docker compose file [$COMPOSE_FILE] not found."
    return 1
  fi

  # Add the root-level external network if it doesn't exist
  # Check if the network 'ndr_bridge_net' is already defined
  network_check=$(yq eval ".networks.${NDR_DOCKER_BRIDGE_NETWORK_NAME}" "$COMPOSE_FILE")
  
  # Now check if it's null or empty
  if [[ "$network_check" == "null" || -z "$network_check" ]]; then
    yq eval ".networks.${NDR_DOCKER_BRIDGE_NETWORK_NAME}.external = true" "$COMPOSE_FILE" -i
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to add root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME'."
      return 1
    else
      ndr_logInfo "Root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' added."
    fi
  else
    ndr_logInfo "Root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' already exists in compose file."
  fi
  
  # Get list of services
  mapfile -t services < <(yq eval '.services | keys | .[]' "$COMPOSE_FILE")
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to retrieve services from compose file [$COMPOSE_FILE]."
    return 1
  fi

  if [[ ${#services[@]} -eq 0 ]]; then
    echo "No services found in compose file."
    return 1
  fi

  # Iterate over services and add network assignment
  for service_name in "${services[@]}"; do
    # Check if service includes the bridge network
    yq eval ".services.${service_name}.networks[]" "$COMPOSE_FILE" | grep -qx "$NDR_DOCKER_BRIDGE_NETWORK_NAME"
    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Service '$service_name' already uses network '$NDR_DOCKER_BRIDGE_NETWORK_NAME', skipping."
      continue
    fi

    yq eval ".services.\"$service_name\".networks += [\"$NDR_DOCKER_BRIDGE_NETWORK_NAME\"]" "$COMPOSE_FILE" -i
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Error adding network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' to service '$service_name'. Aborting."
      return 1
    else
      ndr_logInfo "Network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' added to service '$service_name'."
    fi
  done

  ndr_logInfo "All services updated successfully in compose file [$COMPOSE_FILE]."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_customizeSupabaseDockerComposeFileAddSchema () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file to add schema file"
  ndr_logSecStart "$logSectionDesc"
  
  local COMPOSE_FILE="${gSUPABASE_NEXTDR_HOME}/docker-compose.yml"
  temp_COMPOSE_FILE="${COMPOSE_FILE}.tmp"

  if [[ ! -f $COMPOSE_FILE ]]; then
    ndr_logError "Docker compose file [$COMPOSE_FILE] not found."
    return 1
  fi

  ndr_logInfo "Customizing $gCOMPANY_NAME project Docker compose file [$COMPOSE_FILE]."

  # Replace first occurrence of 'name: supabase' with 'name: ndr-supabase'
  #sed -i '0,/name: supabase/s//name: ndr-supabase/' "$COMPOSE_FILE"
  yq eval -i '.name = "ndr-supabase"' "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to change top level container stack entry to \"ndr-supabase\" in compose file."
    #return 1
  fi
  ndr_logInfo "Changed top level container stack entry to \"ndr-supabase\" in compose file."

  # Skip if both lines are already present
  
  # Check if insert_line1 exists
  # line 1 is a comment line that is used to initialize the database with NDR schema.
  # yq has a limitation with adding comments to an array and it is not critical to have this line in the compose file.
  if false; then
  insert_line1="# Initialize the database with NDR schema"
  grep -qF -- "$insert_line1" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "[$insert_line1] already exists in db volumes"
  else
    yq eval -i ".services.db.volumes += [\"$insert_line1\"]" "$COMPOSE_FILE"
    #yq eval ".services.db.volumes[-1] |= (. style=\"double\" | .comment = \"$insert_line1\")" -i "$COMPOSE_FILE"

    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Added [$insert_line1] to db volumes"
    else
      ndr_logError "Failed to add [$insert_line1] to db volumes"
    fi    
  fi
  fi

  # Check if insert_line2 exists
  insert_line2="./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
  grep -qF -- "$insert_line2" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "[$insert_line2] already exists in db volumes"
  else
    yq eval -i ".services.db.volumes += [\"$insert_line2\"]" "$COMPOSE_FILE"
    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Added [$insert_line2] to db volumes"
    else
      ndr_logError "Failed to add [$insert_line2] to db volumes"
    fi
  fi

  # removing complicated awk logic and using the yq util to simplify the script
  if false; then
  grep -qF -- "$insert_line2" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "$gCOMPANY_NAME schema file entries already present in compose file, skipping addition."
  else
    # if var is not empy AND file exists
    temp_flag="/tmp/awk_entries_added.flag"
    rm -f "$temp_flag"
    if [ -n "$gACTIVE_NDR_SQL_SCHEMA_FILE" ] && [ -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
      # Use awk to inject new lines at the end of the correct 'volumes:' section under 'services -> db'
      entriesAdded=$(awk -v flagfile="$temp_flag" '
        BEGIN {
          in_services = 0
          in_db = 0
          in_volumes = 0
          printed_extra = 0
          entriesAdded = 0
        }

        /^[[:space:]]*services:/ {
          in_services = 1
          print
          next
        }

        in_services && /^[[:space:]]{2}db:$/ {
          in_db = 1
          print
          next
        }

        in_db && /^[[:space:]]{2}[a-z0-9_-]+:$/ {
          in_db = 0
        }

        in_db && /^[[:space:]]+volumes:$/ {
          in_volumes = 1
          volume_indent = gensub(/[^[:space:]].*/, "", "g")
          print
          next
        }

        in_volumes {
          if ($0 ~ "^" volume_indent "  " && ($0 ~ /^ *- / || $0 ~ /^ *#|^ *$/)) {
            print
            next
          } else {
            if (!printed_extra) {
              print volume_indent "  # Initialize the database with NDR schema"
              print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
              printed_extra = 1
              entriesAdded = 1
            }
            in_volumes = 0
            print
            next
          }
        }

        {
          print
        }

        END {
          if (in_volumes && !printed_extra) {
            print volume_indent "  # Initialize the database with NDR schema"
            print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
            entriesAdded = 1
          }

          # Output result to a separate file
          print entriesAdded > flagfile
        }
      ' "$COMPOSE_FILE" > "$temp_COMPOSE_FILE")

      # Read the flag from temp file
      entriesAdded=$(cat "$temp_flag")
      rm -f "$temp_flag"
      if [ "$gDebugMode" -eq 1 ]; then
        ndr_logInfo "Entries added: $entriesAdded"
      fi
      
      if [ "$entriesAdded" -eq 0 ]; then
        ndr_logError "Awk failed to process compose file to add $gCOMPANY_NAME schema file entry to DB volumes section."
        rm -f "$temp_COMPOSE_FILE"
        return 1
      fi

      mv "$temp_COMPOSE_FILE" "$COMPOSE_FILE"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to move temp compose file [$temp_COMPOSE_FILE] to [$COMPOSE_FILE]."
        return 1
      fi
      ndr_logInfo "Moved temp compose file [$temp_COMPOSE_FILE] to [$COMPOSE_FILE]."
      ndr_logInfo "Successfully added $gCOMPANY_NAME schema file entry DB volumes section in compose file."
      
    else
      ndr_logWarn "No active $gCOMPANY_NAME supabase schema file present, skipping sql file injection."
    fi
  fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_verifySupabaseContainersExist ()
{
  local logSectionDesc="Verifying Supabase containers exist"
  ndr_logSecStart "$logSectionDesc"

  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    if [[ "$gNDR_INSTALL_MODE" == true ]]; then
      ndr_RegistryReadServiceEntries
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to read Supabase Docker service entries from registry."
        return 1
      fi
    elif [[ "$gNDR_DEVOPS_MODE" == true ]]; then
      ndr_ParseComposeFileServices "$dockerComposeFile"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to parse Docker compose file [$dockerComposeFile] service entries."
        #return 1
      fi
    else
      ndr_logWarn "Unknown install mode."
      return 1
    fi
  fi

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    ndr_logInfo "No Supabase service array entries found."
    return 1
  fi

  ndr_logInfo "Verifying ${#ndr_supabase_container_services[@]} Supabase service entries."

  local containerFailed=0

  for service in "${ndr_supabase_container_services[@]}"; do
    local container_name="${ndr_supabase_container_service_container_names[$service]}"
    local image_name="${ndr_supabase_container_service_image_names[$service]}"
    local image_tag="${ndr_supabase_container_service_image_tags[$service]}"

    # check if the supabase container exists
    local containerCheck="$container_name" #"$image_name:$image_tag"
    ndr_verifyDockerContainerExists "$containerCheck"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Supabase Docker container [$containerCheck] does not exist."
      containerFailed=1
    fi
  done

  if [ $containerFailed -eq 0 ]; then
    ndr_logInfo "All Supabase Docker containers exist."
  else
    ndr_logError "One or more Supabase Docker containers do not exist. Please check the logs for details."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_UploadSupabaseImages ()
{
  local logSectionDesc="Uploading Supabase Docker Images"
  ndr_logSecStart "$logSectionDesc"

  # Upload the Supabase Docker images
  ndr_logInfo "Uploading Supabase Docker images to repository [$NDR_DOCKER_SUPABASE_REPO_NAME]..."

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    if [[ "$gNDR_INSTALL_MODE" == true ]]; then
      ndr_RegistryReadServiceEntries
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to read Supabase Docker service entries from registry."
        return 1
      fi
    elif [[ "$gNDR_DEVOPS_MODE" == true ]]; then
      ndr_ParseComposeFileServices "$dockerComposeFile"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to parse Docker compose file [$dockerComposeFile] service entries."
        #return 1
      fi
    else
      ndr_logWarn "Unknown install mode."
      return 1
    fi
  fi

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    ndr_logInfo "No Supabase service array entries found."
    return 1
  fi
  
  service="$NDR_SUPABASE_APP_SERVICE_NAME_DB"

  #local container_name="${ndr_supabase_container_service_container_names[$service]}"
  local dockerImageBaseName="${ndr_supabase_container_service_image_names[$service]}"
  local dockerImageVersion="${ndr_supabase_container_service_image_tags[$service]}"

  if [[ -z "$dockerImageBaseName" || -z "$dockerImageVersion" ]]; then
    ndr_logError "Could not find associated Docker image details for service [$service]"
    return 1
  fi

  # do a remote repo check for this image to notify that we will overwrite vs add new.
  ndr_RemoteRepoImageExists "$dockerImageBaseName" "$dockerImageVersion" "$NDR_DOCKER_SUPABASE_REPO_NAME"
  local exists=$?

  # last chance to prompt user for upload
  echo "Uploading Docker image [$dockerImageBaseName:$dockerImageVersion] to Docker Hub Remote Repo [$NDR_DOCKER_SUPABASE_REPO_NAME]."
  if [[ $exists -eq 0 ]]; then
    local remoteRepoImageName
    remoteRepoImageName=$(ndr_LocalToRemoteRepoImageName "$dockerImageBaseName" "$dockerImageVersion" "$NDR_DOCKER_SUPABASE_REPO_NAME")
    echo "Attention: The remote tag [$remoteRepoImageName] for this exact image already exists in the remote hub. Uploading will OVERWRITE the remote image with this version. This action is not reversible!"
  fi
  echo "Are you sure you want to proceed?"
  read -p "Yes to upload or No to skip upload and proceed with remaining operations [Y|n]" -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Nn]$ ]]; then
    ndr_logInfo "Upload cancelled, proceeding with remaining operations."
    return 0
  fi

  ndr_logInfo "Verifying and uploading Docker image for service [$service], image name [$dockerImageBaseName], and image tag [$dockerImageVersion]."

  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  ndr_verifyDockerImageExists "$dockerImageName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image verification failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image verification succeeded for [$dockerImageName]."

  uploadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$NDR_DOCKER_SUPABASE_REPO_NAME" "$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image upload failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image upload succeeded for [$dockerImageName]."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_SupabaseInstallCompleteNotify ()
{
  # Accessing Supabase Studio
    # You can access Supabase Studio through the API gateway on port 8000. For example: http://<your-ip>:8000, or localhost:8000 if you are running Docker locally.

    # You will be prompted for a username and password. By default, the credentials are:

    # Username: supabase
    # Password: this_password_is_insecure_and_should_be_updated

    # Need to change credentials and secrets to secure values
    # Update the ./docker/.env file with your own secrets. In particular, these are required:

    # POSTGRES_PASSWORD: the password for the postgres role.
    # JWT_SECRET: used by PostgREST and GoTrue, among others.

    # Dashboard authentication
    # The Dashboard is protected with basic authentication. The default user and password MUST be updated before using Supabase in production.
    # Update the following values in the ./docker/.env file:

    # DASHBOARD_PASSWORD: The default password for the Dashboard

    ndr_logInfo "Local supabase container installation is complete. You can access the Supabase Studio through the API gateway on port 8000."
    ndr_logInfo "http://<your-ip>:8000, or http://localhost:8000 if you are running # Docker locally"
    ndr_logInfo "Please check the .env file in the $gSUPABASE_NEXTDR_HOME for dashboard credentials."

    return 0
}

function ndr_CreateSupabaseFolders ()
{
   # Create your desired working directory:
    # Tree should look like this
    # .
    # ├── supabase
    # └── supabase-project
    
    local logSectionDesc="Cleaning and creating new application folders"
    ndr_logSecStart "$logSectionDesc"
    
    if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
      ndr_logError "Invalid home or project directory."
      return 1
    fi

    # check up front if schema var is empy or file does not exist before doing any irreversable work
    #if [ -z "$gACTIVE_NDR_SQL_SCHEMA_FILE" ] || [ ! -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    #  ndr_logError "No active $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] found."
    #  return 1
    #fi
    
    rm --recursive --force "$gSUPABASE_NEXTDR_HOME"
    rm --recursive --force "$gSUPABASE_TEMPLATE_HOME"
    mkdir "$gSUPABASE_NEXTDR_HOME"
    mkdir "$gSUPABASE_TEMPLATE_HOME"
    
    ndr_logSecEnd "$logSectionDesc"

    return 0
}

function ndr_PullSupabaseGitTemplate ()
{
  # To pull supabase docker code from GIT
    # This is a very lengthy process and will take several minutes or more depending on connectivity speed.
    local logSectionDesc="Cloning a local supabase template from GIT"
    ndr_logSecStart "$logSectionDesc"

    cd "$gSUPABASE_TEMPLATE_HOME" || { ndr_logError "Failed to cd into supabase template dir [$gSUPABASE_TEMPLATE_HOME]"; return 1; }

    # basic clone command
    #git clone --depth 1 https://github.com/supabase/supabase
    # advanced clone command
    git clone --filter=blob:none --no-checkout https://github.com/supabase/supabase "$gSUPABASE_TEMPLATE_HOME"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Git clone failed."
      return 1
    fi
    ndr_logInfo "Git clone for Supabase completed."

    git sparse-checkout set --cone docker && git checkout master
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "GIT sparse checkout failed."
      return 1
    fi
    ndr_logInfo "Git sparse checkout for Supabase completed."

    ndr_logSecEnd "$logSectionDesc"

    return 0
}

function ndr_CopySupabaseTemplateComposeFiles ()
{
  # Copy the template compose files over to your project
    local logSectionDesc="Copying the template compose files to project"
    ndr_logSecStart "$logSectionDesc"

    # always need to copy the template files because there a number of files and folders other than the main yml needed here.
    cd "$gSCRIPT_HOME_DIR" || { ndr_logError "Failed to cd into install dir"; return 1; }
        
    local srcFile="$gSUPABASE_TEMPLATE_HOME/docker/*"
    local destFile="$gSUPABASE_NEXTDR_HOME"
    cp -rf $srcFile $destFile # note: DO NOT quote the args for cp command, otherwise it will not copy the files correctly with a stat failure.
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy Base template docker files [$srcFile] to [$destFile]"
      return 1
    fi
    ndr_logInfo "Base template docker files [$srcFile] copied to [$destFile]"
    
    ndr_logSecEnd "$logSectionDesc"

    return 0
}

function ndr_CopySupabaseSchemaFile ()
{
  local logSectionDesc="Copying the $gCOMPANY_NAME schema file to project"
    ndr_logSecStart "$logSectionDesc"
    
    # Copy the NDR schema file to the project.
    # This custom sql script will be absorbed by the postgres db docker container at initial startup and will create the NDR tables and RLS policies.
    schemaDestLocation="${gSUPABASE_NEXTDR_HOME}/db-init-scripts"
    mkdir "$schemaDestLocation"
    cp -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" "$schemaDestLocation"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"

    ndr_logSecEnd "$logSectionDesc"

    return 0
}

function ndr_CopySupabaseEnvFile ()
{
  # Copy the custom env vars
  local logSectionDesc="Copying the $gCOMPANY_NAME env file to project"
  ndr_logSecStart "$logSectionDesc"
  
  envSrcFile="${gSCRIPT_HOME_DIR}/${gNDR_ENV_MASTER_FILENAME}"
  envDestFile="${gSUPABASE_NEXTDR_HOME}/${gNDR_ENV_MODULE_FILENAME}"
  cp -f "$envSrcFile" "$envDestFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Docker ENV file [$envSrcFile] to [$envDestFile]"
    return 1
  fi
  ndr_logInfo "$gCOMPANY_NAME Docker ENV file [$envSrcFile] copied to [$envDestFile]"
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_PullSupabaseBaseDockerImages ()
{
  local logSectionDesc="Pulling base Supabase Docker images"
  ndr_logSecStart "$logSectionDesc"
  
  # Go to the docker folder
  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }
  
  # Pull the latest images
  docker compose pull --quiet
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose pull failed"
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

ndr_PullSupabaseNDRDockerImages ()
{

  local logSectionDesc="Pulling $gCOMPANY_NAME Supabase Docker images"
  ndr_logSecStart "$logSectionDesc"

  # Retrieve supabase db image details.
  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    ndr_logInfo "No Supabase service array entries found."
    return 1
  fi
  
  local service="$NDR_SUPABASE_APP_SERVICE_NAME_DB" # the supabase DB
  local container_name="${ndr_supabase_container_service_container_names[$service]}"
  local dockerImageBaseName="${ndr_supabase_container_service_image_names[$service]}"
  local dockerImageVersion="${ndr_supabase_container_service_image_tags[$service]}"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"

  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"

  ndr_DownloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_StartSupabaseDockerContainers ()
{
  # Start the container services (in detached mode)
  local logSectionDesc="Starting Supabase Docker Container services"
  ndr_logSecStart "$logSectionDesc"
  
  # Go to the docker folder
  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }

  dockerContainerName="$NDR_SUPABASE_CONTAINER_NAME"

  docker compose -p "$dockerContainerName" up -d
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose up failed"
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_CleanupSupabaseProjectDirs ()
{
  local logSectionDesc="Cleaning up Supabase Docker folders"
  ndr_logSecStart "$logSectionDesc"

  # clean up supabase folders
  rm -rf "$gSUPABASE_TEMPLATE_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove Supabase folder [$gSUPABASE_TEMPLATE_HOME]"
    #return 1
  fi

  if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
    rm -rf "$gSUPABASE_NEXTDR_HOME"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove Supabase folder [$gSUPABASE_NEXTDR_HOME]"
      #return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_StopSupabaseDockerContainers ()
{
  # bring the containers down.
  local logSectionDesc="Stopping Supabase Docker containers"
  ndr_logSecStart "$logSectionDesc"
  
  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }
  
  docker compose down
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logInfo "Failed to stop Supabase Docker containers"
    return 0
  fi
  ndr_logInfo "Stopped Supabase Docker containers"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_BuildSupabaseApplication ()
{
  local logSectionDesc="Building and Installing Supabase Docker application"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logError "No build options specified."
    return 1
  fi

  ndr_PrepareSupabaseInstallation "$appBuildOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Supabase prepare step failed."
    return 1
  fi

  ndr_ConfigureSupabaseInstallation "$appBuildOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Supabase configure step failed."
    return 1
  fi
  
  ndr_InstallSupabaseApplication "$appBuildOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Supabase install step failed."
    return 1
  fi
  
  ndr_PostInstallSupabaseApplication "$appBuildOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Supabase post install step failed."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_PrepareSupabaseInstallation ()
{
  local logSectionDesc="Prepare Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logError "No build options specified."
    return 1
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL_PREREQUISITES )); then
    ndr_packagePrereqCheck
    return_code=$?
    if [ $return_code -ne 0 ]; then
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_HOME_DIR_CHECK )); then
    if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
      gNEXTDR_HOME_DIR="$PWD"
    else
      ndr_InstallHomeCheck
      return_code=$?
      if [ $return_code -ne 0 ]; then
        return 1
      fi
    fi

    gSUPABASE_NEXTDR_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_NEXTDR_SUB}"
    gSUPABASE_TEMPLATE_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_TEMPLATE_SUB}"
  fi
  
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_EXISTING_APP )); then
    local cleanupOptions=$NDR_SUPABASE_CLEANUP_OPTION_BEST_EFFORT
    ndr_SupabaseContainerCleanup $cleanupOptions
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to clean up Supabase containers."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_ConfigureSupabaseInstallation ()
{
  local logSectionDesc="Configuring Supabase Installation"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logError "No build options specified."
    return 1
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_DIRS )); then
    ndr_CreateSupabaseFolders
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to create Supabase folders."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_GIT_CLONE_TEMPLATE )); then
    ndr_PullSupabaseGitTemplate
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to pull Supabase git template."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_COMPOSE_FILES )); then
    ndr_CopySupabaseTemplateComposeFiles
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to copy Supabase template compose files."
      return 1
    fi
  fi
  
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CUSTOMIZE_COMPOSE_FILE )); then
    ndr_customizeSupabaseDockerComposeFile "$appBuildOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to customize $gCOMPANY_NAME Docker compose file."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_QUERY_COMPOSE_SERVICES )); then
    local dockerComposeFile="$gSUPABASE_NEXTDR_HOME/docker-compose.yml"
    ndr_ParseComposeFileServices "$dockerComposeFile"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to parse Docker compose file [$dockerComposeFile] service entries."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_InstallSupabaseApplication ()
{
  local logSectionDesc="Installing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logError "No build options specified."
    return 1
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_SUPABASE_APP_REGISTRY )); then
    ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      return 1
    fi
    ndr_logInfo "Successfully created registry."

    ndr_RegistryAddKeyServiceEntries
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to add Docker service entries to registry"
      return 1
    fi
    ndr_logInfo "Successfully added Docker compose file service entries to registry."
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_SCHEMA_FILE_CHECK )); then
    ndr_SupabaseLocalSchemaFileCheck
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "No active $gCOMPANY_NAME Supabase schema file found."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_SCHEMA_FILE )); then
    ndr_CopySupabaseSchemaFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Supabase schema file."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_COPY_ENV_FILE )); then
    ndr_CopySupabaseEnvFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Supabase Env file."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_SUPABASE_BASE_IMAGES )); then
    ndr_PullSupabaseBaseDockerImages
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to pull Supabase base Docker images."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_PULL_NDR_SUPABASE_IMAGES )); then
    ndr_PullSupabaseNDRDockerImages
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to pull Supabase $gCOMPANY_NAME custom Docker images."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CREATE_BRIDGE_NETWORK )); then
    # create the bridge network if it does not exist
    ndr_createDockerBridgeNetwork
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to create Docker bridge network."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_START_CONTAINERS )); then
    ndr_StartSupabaseDockerContainers
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to start Supabase $gCOMPANY_NAME Docker containers."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_VERIFY_CONTAINERS )); then
    # check if the newly built Docker containers exist
    ndr_verifySupabaseContainersExist
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker container verification failed."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_APP_INSTALL_COMPLETE_NOTIFY )); then
    ndr_SupabaseInstallCompleteNotify
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_PostInstallSupabaseApplication ()
{
  local logSectionDesc="Post install Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="${1:$NDR_SUPABASE_APP_BUILD_OPTIONS_NONE}"
  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_NONE )); then
    ndr_logError "No build options specified."
    return 1
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_STOP_CONTAINERS )); then
    ndr_StopSupabaseDockerContainers
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to stop Supabase Docker containers."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_UPLOAD_NDR_SUPABASE_IMAGES )); then
    ndr_UploadSupabaseImages
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to upload Supabase images."
      return 1
    fi
  fi
  
  if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
    echo "Supabase application build is now complete. Would you like to keep and start the application or continue to remove and clean up?"
    read -p "Yes to keep and start or No to remove and clean up [Y|n]" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Cleanup chosen, proceeding to remove application."
    else
      ndr_logInfo "Keep application chosen, proceeding to restart application."

      ndr_StartSupabaseDockerContainers
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to start Supabase $gCOMPANY_NAME Docker containers."
        return 1
      fi

      # exit here before any further cleanup.
      return 0
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_REMOVE_SUPABASE_APP )); then
    ndr_SupabaseContainerCleanup
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to clean up Supabase containers."
      return 1
    fi
  fi

  if (( appBuildOptions & NDR_SUPABASE_APP_BUILD_OPTIONS_CLEANUP_SUPABASE_DIRS )); then
    ndr_CleanupSupabaseProjectDirs
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to clean up Supabase Docker folders."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
