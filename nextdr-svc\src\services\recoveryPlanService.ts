import { supabase } from "../db/supabaseClient";
import { RecoveryPlan } from "../models/gcp_backup";

// get all recovery plans
export const getRecoveryPlans = async () => {
	const { data, error } = await supabase.from("recovery_plans_new").select("*");
};

// get recovery plan by id
export const getRecoveryPlanById = async (id: string) => {
	const { data, error } = await supabase
		.from("recovery_plans_new")
		.select("*")
		.eq("id", id)
		.single();

	if (error) {
		console.error("Error getting recovery plan:", error);
		throw error;
	}
	return data;
};

// create a recovery plan
export const createRecoveryPlan = async (recoveryPlan: RecoveryPlan) => {
	const { data, error } = await supabase.from("recovery_plans_new").insert(recoveryPlan);

	if (error) {
		console.error("Error creating recovery plan:", error);
		throw error;
	}
	return data;
};

// update a recovery plan
export const updateRecoveryPlan = async (id: string, recoveryPlan: RecoveryPlan) => {
	const { data, error } = await supabase
		.from("recovery_plans_new")
		.update(recoveryPlan)
		.eq("id", id);

	if (error) {
		console.error("Error updating recovery plan:", error);
		throw error;
	}
	return data;
};

// delete a recovery plan
export const deleteRecoveryPlan = async (id: string) => {
	const { data, error } = await supabase.from("recovery_plans_new").delete().eq("id", id);
};

export const getRestoreVirtualMachineStepIdForRecoveryPlan = async (recoveryPlanId: string) => {
	const { data, error } = await supabase
		.from("recovery_steps_new")
		.select("*")
		.eq("recovery_plan_id", recoveryPlanId)
		.eq("operation_type", "Restore virtual machine")
		.single();

	if (error) {
		console.error("Error getting restore virtual machine step id for recovery plan:", error);
		throw error;
	}
	return data;
};
