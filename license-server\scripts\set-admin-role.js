/**
 * <PERSON><PERSON><PERSON> to set a user's role to admin
 *
 * Usage: node scripts/set-admin-role.js <email>
 *
 * This script requires the Supabase anonymous key to be set in the environment
 * as NEXT_PUBLIC_SUPABASE_ANON_KEY
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = "https://utcestwwfefexcnmjcjr.supabase.co";
const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0Y2VzdHd3ZmVmZXhjbm1qY2pyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNjY4MTk1NiwiZXhwIjoyMDQyMjU3OTU2fQ.GhSBwLZ7qY-zt1T5nHXfMPy3QdNJiVQ8KG9PMAPeCLA"

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const email = process.argv[2];

if (!email) {
  console.error('Usage: node scripts/set-admin-role.js <email>');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setAdminRole(userEmail) {
  try {
    // Get user by email
    const { data: users, error: getUserError } = await supabase.auth.admin.listUsers();

    if (getUserError) {
      throw getUserError;
    }

    const user = users.users.find(u => u.email === userEmail);

    if (!user) {
      console.error(`User with email ${userEmail} not found`);
      return;
    }

    // Update user metadata to set role as admin
    const { data, error } = await supabase.auth.admin.updateUserById(user.id, {
      user_metadata: {
        ...user.user_metadata,
        role: 'admin'
      }
    });

    if (error) {
      throw error;
    }

    console.log(`Successfully set admin role for user: ${userEmail}`);
    console.log('User metadata:', data.user.user_metadata);

  } catch (error) {
    console.error('Error setting admin role:', error.message);
  }
}

setAdminRole(email);
