import { NextResponse } from "next/server";
import { verifyLicense } from "../../../../lib/utils/license";
import { cookies } from "next/headers";

export async function GET() {
	try {
		const cookieStore = cookies();
		const licenseKey = cookieStore.get("license")?.value;

		if (!licenseKey) {
			return NextResponse.json({ valid: false, error: "No license token found" }, { status: 401 });
		}

		const license = await verifyLicense(licenseKey);

		return NextResponse.json({
			valid: true,
			customerId: license.customer_id,
			expiresAt: license.expires_at,
			features: license.features || [],
		});
	} catch (error: any) {
		return NextResponse.json(
			{
				valid: false,
				error: error.message,
			},
			{ status: 401 }
		);
	}
}
