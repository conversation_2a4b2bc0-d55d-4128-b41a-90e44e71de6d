#!/bin/bash
# ndrCommon.sh - A Bash module providing common definitions and functions

# company/product naming
export gCOMPANY_NAME="orKestrate.Ai"
export gPRODUCT_NAME="NextDR"
export gPRODUCT_NAME_SHORT="ndr"
export gPRODUCT_VERSION="v1.0"

# registry
export gREGISTRY_DIR="/var/lib/$gPRODUCT_NAME"
export gREGISTRY_FILE="$gREGISTRY_DIR/registry.json"
export gREGISTRY_ENTRY_VERSION="version"
export gREGISTRY_ENTRY_INSTALL_DATE="install_date"
export gREGISTRY_ENTRY_UPDATE_DATE="update_date"
export gREGISTRY_ENTRY_HOME_DIR="home_dir"

export gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME="module_status_supabase"
export gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME="module_status_service"
export gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME="module_status_ui"
export gREGISTRY_ENTRY_MODULE_STATUS_INSTALLED="installed"
export gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED="not_installed"

export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICES="supabase_docker_services"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_NAME="service_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_CONTAINER_NAME="container_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_IMAGE_NAME="image_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_IMAGE_TAG="image_tag"

# env files
export gNDR_ENV_MASTER_FILENAME=".env.nextdr" # master environment file for NextDR
export gNDR_ENV_MODULE_FILENAME=".env"

# supabase
# Global arrays
declare -a ndr_supabase_container_services=()
declare -A ndr_supabase_container_service_container_names=()
declare -A ndr_supabase_container_service_image_names=()
declare -A ndr_supabase_container_service_image_tags=()


# common
export gPrereqCheckComplete=0 # flag to indicate if prereq checks have been completed and not needed to be run again on subsequent interactive commands
export gNEXTDR_HOME_DIR="/opt/$gPRODUCT_NAME_SHORT/home" #"$PWD"
export gSCRIPT_HOME_DIR="$PWD"
export gLOG_FILE=""
export gExpressMode=0
export gExpressOption=""
export gDebugMode=0

[[ -n "$_NDR_COMMON_LOADED" ]] && return
_NDR_COMMON_LOADED=1

# package prereq versions
NDR_MIN_REQUIRED_VERSION_DEFAULT="0.0.0"
NDR_MIN_REQUIRED_YQ_VERSION="1.0.0"

# git
export NDR_GITHUB_REPO_USERNAME="kamlad"
export NDR_GITHUB_REPO_NAME="nextdr"
export NDR_GIT_REPO_BRANCH_OR_TAG="master" # could be a branch or tag
export NDR_GIT_LOCAL_REPO_DEST_DIR="" #"$HOME/dev/$NDR_GITHUB_REPO_NAME"
export NDR_GITHUB_REPO_URL="**************:$NDR_GITHUB_REPO_USERNAME/$NDR_GITHUB_REPO_NAME.git"
export NDR_GIT_SSH_KEY_USER_EMAIL="<EMAIL>"

export NDR_GIT_LOCAL_REPO_USER_NAME="jfutey"
export NDR_GIT_LOCAL_REPO_USER_EMAIL="<EMAIL>"

# ssh
export gSSH_Passphrase="nextdr!12"
export SSH_KEY_FILE_NAME="id_ed25519"
export SSH_KEY_DIR="$HOME/.ssh"
export SSH_KEY_FILE="$SSH_KEY_DIR/$SSH_KEY_FILE_NAME"
export SSH_PUB_KEY_FILE="${SSH_KEY_FILE}.pub"
export SSH_PUBLIC_KEY=""
export gSSH_KEY_AUTHENTICATED=0

# supabase variables
export gSUPABASE_CLI_CMD="npx supabase"

# Os type
export osTypeMajor=""
export distroFamily=""
export distroId=""

# docker repo
export NDR_DOCKER_REPO_ACCOUNT="jfutey"
export NDR_DOCKER_REPO_ACCESS_TOKEN="************************************"
export NDR_DOCKER_SUPABASE_REPO_NAME="orkestrate-ai" #"orkestrate-supabase"
export NDR_DOCKER_SERVICE_REPO_NAME="orkestrate-ai" #"orkestrate-svc"
export NDR_DOCKER_UI_REPO_NAME="orkestrate-ai" #"orkestrate-ui"

# docker image repo/storage type
export NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB=1 # this mode directs images to be uploaded to the docker hub repo
export NDR_DOCKER_IMAGE_REPO_TYPE_GIT=2 # this mode directs images to be uploaded to git
export NDR_DOCKER_IMAGE_REPO_TYPE=$NDR_DOCKER_IMAGE_REPO_TYPE_GIT # current default image upload mode -- change as needed in production.

# docker bridge network
export NDR_DOCKER_BRIDGE_NETWORK_NAME="ndr_bridge_net"

# docker app build enum
export NDR_DOCKER_APP_MANAGE_OPTIONS_NONE=0x0
export NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE=0x1 # build/remove image only.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER=0x2 # build/remove container only. In build mode, does not interactively prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL=0x4 # Optional flag for container. In build mode, this triggers an interactive prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT=0x8 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT=0x10 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_BUILD_IMAGE_AND_CONTAINER=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )) # build image and container (does not prompt for container creation).
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT )) # everything.
export NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL )) # standard behavior of create image, then prompt for optional operations such as module preclean and continer build/start.

# docker service app
export NDR_SERVICE_HOME_LOC="nextdr-svc"
export NDR_SERVICE_IMAGE_NAME="nextdr-svc-img"
export NDR_SERVICE_IMAGE_VERSION="1.0.0"
export NDR_SERVICE_CONTAINER_NAME="nextdr-svc-app"
export NDR_SERVICE_CONTAINER_PORT="8081"
export NDR_SERVICE_DOCKERFILE_FILENAME="Dockerfile-svc"
export NDR_SERVICE_COMPOSEFILE_FILENAME="docker-compose-svc.yaml"

# docker ui app
export NDR_UI_HOME_LOC="nextdr-ui"
export NDR_UI_IMAGE_NAME="nextdr-ui-img"
export NDR_UI_IMAGE_VERSION="1.0.0"
export NDR_UI_CONTAINER_NAME="nextdr-ui-app"
export NDR_UI_CONTAINER_PORT="80"
export NDR_UI_DOCKERFILE_FILENAME="Dockerfile-ui"
export NDR_UI_COMPOSEFILE_FILENAME="docker-compose-ui.yaml"

export NDR_DOCKER_MODULE_NAME_DELIMITER_RAW="/"
export NDR_DOCKER_MODULE_NAME_DELIMITER_MONIKER="_NDR_DELIM_"

# docker supabase app
export NDR_SUPABASE_HOME_LOC="$gSUPABASE_NEXTDR_SUB"
#export NDR_SUPABASE_IMAGE_NAME="nextdr-supabase-img"
#export NDR_SUPABASE_IMAGE_VERSION="1.0.0"
export NDR_SUPABASE_CONTAINER_NAME="ndr-supabase"
export NDR_SUPABASE_CONTAINER_PORT="8000"

# older version of Docker
dockerRemovePkgs=(
  docker.io
  docker-doc
  docker-compose
  docker-compose-v2
  podman-docker
  containerd
  runc
)

# newer version of Docker
dockerInstallPackages=(
  "docker-ce" \
  "docker-ce-cli" \
  "containerd.io" \
  "docker-buildx-plugin" \
  "docker-compose-plugin"
)
  
  
# ANSI color codes or logging
RESET="\033[0m"
BOLD="\033[1m"
RED="\033[31m"
YELLOW="\033[33m"
TEAL="\033[36m"
GREEN="\033[32m"
BLUE="\033[34m"

# ----------------------

# --- PROTOS ---

# Function prototypes


# --- FUNCTIONS ---

function ndr_logSecStart () 
{
  echo -e "${BLUE}+++ START [${RESET} $1 ${BLUE}] +++${RESET}"
}

function ndr_logSecEnd () 
{
  echo -e "${GREEN}--- COMPLETE [${RESET} $1 ${GREEN}] ---${RESET}"
}

function _log_format () 
{
  local caller="$1" # optional, can be blank.
  local color="$2" # required
  local level="$3" # required
  local message="$4" # required

  local datetime="[$(date '+%Y-%m-%d %H:%M:%S')]"
  
  if [[ -n "$caller" ]]; then
    caller=" $caller"
  fi

  echo -e "${datetime}${caller} ${color}${level}${RESET} ${message}"
}

function ndr_logInfo () 
{
  #echo -e "\033[36mℹ️ INFO: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$TEAL" "ℹ️ INFO" "$1"
}

function ndr_logWarn () 
{
  #echo -e "\033[33m⚠️ WARNING: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$YELLOW" "⚠️ WARNING" "$1"
}

function ndr_logError () 
{
  #echo -e "\033[31m❌ ERROR: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$RED" "❌ ERROR" "$1"
}

# Usage: function <value> <flag>
# Returns 0 (true) if the flag is set, 1 (false) otherwise
function ndr_checkFlag () 
{
  local value="$1"
  local flag="$2"

  if (( (value & flag) != 0 )); then
    return 0  # flag is set
  else
    return 1  # flag is not set
  fi
}

function ndr_osTypeCheck () 
{
  local logSectionDesc="Operating system type check"
  ndr_logSecStart "$logSectionDesc"

  os_type=$(uname -s)
  case "$os_type" in
    Linux)
      osTypeMajor="Linux"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    unix*)
      osTypeMajor="Unix"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    Windows*)
      osTypeMajor="Windows"
      gSUPABASE_CLI_CMD="npx supabase"
      ;;
    *NT*)
      osTypeMajor="Windows"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    *)
      osTypeMajor="Unknown"
      ;;
  esac

  ndr_logInfo "Operating system is [$osTypeMajor], type [$os_type | $OSTYPE]"
  
  if [[ "$osTypeMajor" == "Windows" ]]; then
    distroFamily="Windows"
    distroId="Windows"
    ndr_logSecEnd "$logSectionDesc"
    return 0
  fi

  # Try to read from /etc/*release
  if grep -q "^ID=" /etc/*release 2>/dev/null; then
    distroId=$(grep "^ID=" /etc/*release | head -n1 | cut -d= -f2 | tr -d '"' | tr '[:upper:]' '[:lower:]')
  elif [[ "$(uname)" == "Darwin" ]]; then
    distroFamily="osx"
    distroId="Darwin"
  else
    ndr_logError "Unable to detect distribution."
    return 1
  fi

  # Map to main distro families
  case "$distroId" in
    ubuntu|debian|linuxmint|elementary|pop|zorin)
      distroFamily="debian"
      ;;
    rhel|redhat|centos|fedora|rocky|almalinux|scientific)
      distroFamily="redhat"
      ;;
    arch|manjaro|endeavouros)
      distroFamily="arch"
      ;;
    opensuse|suse|sles|opensuse-tumbleweed|suse-leap|opensuse-leap|suse-sles)
      distroFamily="suse"
      ;;
    alpine)
      distroFamily="alpine"
      ;;
    osx|darwin)
      distroFamily="osx"
      ;;
    *)
      distroFamily="unknown"
      ;;
  esac

  ndr_logInfo "Detected distro: $distroId, family: $distroFamily."

  # set os/disto specific commands

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_parseCommandLineArgs ()
{
  local logSectionDesc="Parsing common command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Enable logging only if gLOG_FILE is non-empty
  if [[ -n "$gLOG_FILE" ]]; then
    #exec > >(tee -a "$gLOG_FILE") 2>&1
    exec &> >(tee -a "$gLOG_FILE")
  fi

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --express|-e)
        if [[ -n "$2" && "$2" != --* ]]; then
          gExpressMode=1
          gExpressOption="$2"
          ndr_logInfo "Express option set to [$gExpressOption]"
          shift 2
        else
          ndr_logError "--express requires a value."
          return 1
        fi
        ;;
      --debug)
        gDebugMode=1
        shift
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Function to compare semantic versions
# Returns 0 (true) if $1 < $2
# Returns 1 (false) if $1 >= $2
function version_lt() 
{
  [ "$(printf '%s\n' "$1" "$2" | sort -V | head -n1)" != "$2" ]
}

function ndr_checkAndInstallPrerequisites () 
{
  local logSectionDesc="Checking prerequisites"
  ndr_logSecStart "$logSectionDesc"

  if [[ $gPrereqCheckComplete -eq 1 ]]; then
    ndr_logInfo "Prerequesite check complete, skipping."
    return 0
  fi

  local checkOnlyFlag=false
  if [[ "$1" == "checkOnly" ]]; then
    checkOnlyFlag=true
  fi

  declare -A mapPackageAndVersions=()

  mapPackageAndVersions["yq"]="$NDR_MIN_REQUIRED_YQ_VERSION"
  mapPackageAndVersions["npm"]="$NDR_MIN_REQUIRED_VERSION_DEFAULT"
  mapPackageAndVersions["npx"]="$NDR_MIN_REQUIRED_VERSION_DEFAULT"
  mapPackageAndVersions["git"]="$NDR_MIN_REQUIRED_VERSION_DEFAULT"
  mapPackageAndVersions["docker"]="$NDR_MIN_REQUIRED_VERSION_DEFAULT"
  #mapPackageAndVersions["$gSUPABASE_CLI_CMD"]="$NDR_MIN_REQUIRED_VERSION_DEFAULT"
  
  local packageInstalled=false

  for pkg in "${!mapPackageAndVersions[@]}"; do
    local minPkgVersion="${mapPackageAndVersions[$pkg]}"
    
    # assume all packages are not installed by default, then check one by one.
    packageInstalled=false
    local removeRequired=false

    while true; do
      if ! command -v "$pkg" &> /dev/null; then
        ndr_logWarn "$pkg requires installation."
        break
      fi
      
      # some version of package is least installed, check version independently below.
      packageInstalled=true # package is installed
      
      # only check version if min required version for package is populated with valid value.
      if [[ "$minPkgVersion" == "$NDR_MIN_REQUIRED_VERSION_DEFAULT" ]]; then
        # package is installed and version check is not required.
        break
      fi

      local pkgOutput=$("$pkg" --version)
      local return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logWarn "Error scraping $pkg version output [$return_code -> $pkgOutput]"
        packageInstalled=false
        break
      fi
      
      # parse the installed numerical version from the command output. If version number not present, skip version checks.
      local pkgInstalledVersion=$(echo "$pkgOutput" | sed -nE 's/.*([0-9]+\.[0-9]+\.[0-9]+).*/\1/p')
      if [[ -z $pkgInstalledVersion ]]; then
        # unable to parse out specific version info, skip further processing.
        ndr_logWarn "Version info not found for package [$pkg], [$pkgOutput]"
        packageInstalled=false
        break
      fi
      ndr_logInfo "Found [$pkg] package version installed [$pkgInstalledVersion] compared to min reqd version [$minPkgVersion]"
      
      version_lt "$pkgInstalledVersion" "$minPkgVersion"
      return_code=$?
      if [ $return_code -eq 0 ]; then
        ndr_logInfo "Older version of [$pkg] detected. Replacing with latest..."
        removeRequired=true # removal of old version will be required.
        packageInstalled=false # then installation of new version.
      else
        ndr_logInfo "✅ Package [$pkg] is up to date."
      fi
      
      break
    done

    if [ $packageInstalled == true ]; then
      # package is installed and version is matching, nothing more to do.
      ndr_logInfo "✅ $pkg is installed."
      continue;
    fi

    if [ $checkOnlyFlag == true ]; then
      # executed in check only mode, do not actually install any packages.
      continue
    fi

    
    read -p "Would you like to install/update $pkg now? [Y/n] " REPLY
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logWarn "Skipping installation of $pkg."
      continue;
    fi

    ndr_logInfo "Installing [$pkg] package..."
    case "$pkg" in
      yq)
        packageURL="https://github.com/mikefarah/yq"

        if [ $removeRequired == true ]; then
          if [[ "$distroFamily" == "debian" ]]; then
            cmd="sudo apt-get remove --assume-yes yq"
          elif [[ "$distroFamily" == "redhat" ]]; then
            cmd="sudo dnf remove yq"
          elif [[ "$distroFamily" == "suse" ]]; then
            cmd="sudo zypper remove --no-confirm yq"
          elif [[ "$distroFamily" == "osx" ]]; then
            cmd="brew remove yq"
          fi

           $cmd
          return_code=$?
          if [ $return_code != 0 ]; then
            ndr_logError "[$cmd] failure, $pkg package not removed, please manually remove package before proceeding"
            return 1
          fi
        fi

        cmd="wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/local/bin/yq"
        $cmd
        return_code=$?
        if [ $return_code != 0 ]; then
          ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
          return 1
        fi
        sudo chmod +x /usr/local/bin/yq

        ndr_logInfo "$pkg package installed successfully."
        ;;

      awk|gawk)
        packageURL="https://www.gnu.org/software/gawk/manual/gawk.html"
        
        if [[ "$distroFamily" == "debian" ]]; then
          cmd="sudo apt-get install --assume-yes gawk"
        elif [[ "$distroFamily" == "redhat" ]]; then
          cmd="sudo dnf install gawk"
        elif [[ "$distroFamily" == "suse" ]]; then
          cmd="sudo zypper install --no-confirm gawk"
        elif [[ "$distroFamily" == "osx" ]]; then
          cmd="brew install gawk"
        fi
        
        $cmd
        return_code=$?
        if [ $return_code != 0 ]; then
          ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
          return 1
        fi
        ndr_logInfo "$pkg package installed successfully."
        ;;

      npm|npx)
        packageURL="https://docs.npmjs.com/downloading-and-installing-node-js-and-npm"
        
        if [[ "$distroFamily" == "debian" ]]; then
          cmd="sudo apt install --assume-yes npm"
        elif [[ "$distroFamily" == "redhat" ]]; then
          cmd="sudo dnf install nodejs"
        elif [[ "$distroFamily" == "suse" ]]; then
          cmd="sudo zypper install --no-confirm nodejs"
        elif [[ "$distroFamily" == "osx" ]]; then
          cmd="brew install node"
        fi
        
        $cmd
        return_code=$?
        if [ $return_code != 0 ]; then
          ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
          return 1
        fi
        ndr_logInfo "$pkg package installed successfully."
        ;;

      git)
        packageURL="https://git-scm.com/downloads"
        
        if [[ "$distroFamily" == "debian" ]]; then
          cmd="sudo apt install --assume-yes git"
        elif [[ "$distroFamily" == "redhat" ]]; then
          cmd="sudo dnf install git"
        elif [[ "$distroFamily" == "suse" ]]; then
          cmd="sudo zypper install --no-confirm git"
        elif [[ "$distroFamily" == "osx" ]]; then
          cmd="brew install git"
        fi

        $cmd
        return_code=$?
        if [ $return_code != 0 ]; then
          ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
          return 1
        fi
        ndr_logInfo "$pkg package installed successfully."
        ;;

      docker)
        packageURL="https://docs.docker.com/engine/install/"
        if [[ "$distroFamily" == "debian" ]]; then
          local installCmds=(
            ndr_checkAndInstallDocker
          )
        elif [[ "$distroFamily" == "debianOLD" ]]; then
          local installCmds=(
                "sudo apt-get install --assume-yes gnome-terminal"
                "sudo apt-get update" \
                "sudo apt-get install --assume-yes ca-certificates" \
                "sudo apt-get install --assume-yes curl" \
                "sudo install -m 0755 -d /etc/apt/keyrings" \
                "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
                "sudo chmod a+r /etc/apt/keyrings/docker.asc" \
                #"echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list"
                # Step 1: Get system architecture
                "arch=$(dpkg --print-architecture)" \
                # Step 2: Get Ubuntu codename (e.g., focal, jammy)
                ". /etc/os-release" \
                "codename=\"${UBUNTU_CODENAME:-$VERSION_CODENAME}\"" \
                # Step 3: Construct the deb line
                "deb_line=\"deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable\"" \
                # Step 4: Write to APT sources list with sudo
                "echo \"$deb_line\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null" \
                "sudo apt-get update" \
                "sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin")
        elif [[ "$distroFamily" == "redhat" ]]; then
          local installCmds=(
                "sudo dnf install gnome-terminal" \
                "sudo dnf -y install dnf-plugins-core" \
                "sudo dnf-3 config-manager --add-repo https://download.docker.com/linux/fedora/docker-ce.repo" \
                "sudo dnf install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin" \
                "sudo systemctl enable --now docker")
        elif [[ "$distroFamily" == "suse" ]]; then
          local installCmds=(
                "sudo zypper addrepo https://download.opensuse.org/repositories/Virtualization:containers/openSUSE_Tumbleweed_and_d_l_g/Virtualization:containers.repo" \
                "sudo zypper addrepo https://download.docker.com/linux/sles/docker-ce.repo" \
                "sudo zypper refresh" \
                "sudo zypper install docker-compose" \
                "sudo systemctl enable docker.service" \
                "sudo usermod -aG docker $USER" \
                "sudo systemctl start docker.service" \
                "sudo zypper install docker")
        fi

        for installCmd in "${installCmds[@]}"; do
        ndr_logInfo "Executing installation command [$installCmd]..."
          $installCmd
          return_code=$?
          if [ $return_code != 0 ]; then
            ndr_logError "[$installCmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
            # skip apt-get command failures
            if [[ "$installCmd" == *"apt-get update"* || "$installCmd" == *addrepo* ]]; then
              continue
            fi
            return 1
          fi
        done
        
        getent group docker || sudo groupadd docker
        return_code=$?
        if [ $return_code -ne 0 ]; then
          ndr_logError "Failed to create docker group. Please check your system configuration."
          return 1
        fi
        ndr_logInfo "Created docker group if it did not exist."

        sudo usermod -aG docker "$USER"
        return_code=$?
        if [ $return_code -ne 0 ]; then
          ndr_logError "Failed to add user $USER to docker group. Please check your system configuration."
          return 1
        fi
        ndr_logInfo "User $USER added to docker group. You may need to log out and back in for this change to take effect."

        if systemctl is-active --quiet docker; then
          ndr_logInfo "Docker is running."
        else
          # Optional: try to start it
          ndr_logInfo "Docker is NOT running, attempting to start Docker..."
          
          sudo systemctl start docker

          # Check again after attempt
          if systemctl is-active --quiet docker; then
              ndr "Docker started successfully."
          else
              ndr_logError "Failed to start Docker. Check logs with: journalctl -u docker. A reboot may be required to complete the installation."
              return 1
          fi
        fi
        
        ndr_logInfo "$pkg package installed successfully."            
        ;;

      supabase)
        packageURL="https://supabase.com/docs/guides/local-development"

        cmd="sudo npm install supabase --save-dev"
        
        $cmd
        return_code=$?
        if [ $return_code != 0 ]; then
          ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
          return 1
        fi
        ndr_logInfo "$pkg package installed successfully."
        ;;
      
      *)
        ndr_logWarn "Unknown package [$pkg] "
        continue
        ;;
    esac

    packageInstalled=true

  done

  # the prereq check complete flag needs to be qualfied by all packages installed before setting
  if [ $packageInstalled == true ]; then
    ndr_logInfo "All required packages are installed."
    gPrereqCheckComplete=1
  else
    ndr_logError "Some required packages are not installed. Please install them before proceeding."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


function ndr_checkAndInstallDocker ()
{
  # -------------------------------------
  # cleanup old docker packages

  local logSectionDesc="Conflicting Docker package removal"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"
  
  # -------------------------------------
  # 🚮 Remove any stale Docker APT source files

  local logSectionDesc="🧹 Cleaning up old Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Remove docker.list if it exists
  docker_list="/etc/apt/sources.list.d/docker.list"
  if [[ -f "$docker_list" ]]; then
    sudo rm -f "$docker_list"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "file $docker_list not removed, please manually remove file before proceeding."
      return 1
    fi
    ndr_logInfo "🗑️ Removed stale Docker source file."
  fi

  # Optional: grep and warn about any other Docker-related entries
  if grep -r "download.docker.com" /etc/apt/sources.list.d/ > /dev/null; then
    ndr_logWarn "Found other Docker source entries in [/etc/apt/sources.list.d/]."
  fi

  if grep -q "download.docker.com" /etc/apt/sources.list; then
    ndr_logWarn "Found Docker source entry in [/etc/apt/sources.list]  You may want to clean that manually.033[0m"
  fi

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # Add Docker's official GPG key:

  local logSectionDesc="Adding Official Docker GPG Key"
  ndr_logSecStart "$logSectionDesc"

  sudo apt-get update
  pkgs=(
    ca-certificates
    curl
  )
  # Loop through each package and install it
  for pkg in "${pkgs[@]}"; do
    ndr_logInfo "💾 Installing $pkg..."
    sudo apt-get install --assume-yes "$pkg"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "$pkg package not installed, please manually install package before proceeding."
      return 1
    fi
  done

  local cmds=(
    "sudo install -m 0755 -d /etc/apt/keyrings" \
    "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
    "sudo chmod a+r /etc/apt/keyrings/docker.asc"
    )
  
  for cmd in "${cmds[@]}"; do
    ndr_logInfo "💾 Executing command [$cmd]..."
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Command [$cmd] failure, please run manually before proceeding."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # add deb line to apt sources list

  local logSectionDesc="Adding Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"
  
  # single line execution
  #echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list

  # multi line execution
  # Step 1: Get system architecture
  arch=$(dpkg --print-architecture)

  # Step 2: Get Ubuntu codename (e.g., focal, jammy)
  . /etc/os-release
  codename="${UBUNTU_CODENAME:-$VERSION_CODENAME}"

  # Step 3: Construct the deb line
  deb_line="deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable"

  # Step 4: Write to APT sources list with sudo 
  apt_source_file="/etc/apt/sources.list.d/docker.list"
  echo "$deb_line" | sudo tee "$apt_source_file" > /dev/null
  
  ndr_logInfo "Adding deb line [$deb_line] to list file [$apt_source_file]."

  sudo apt-get update

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # verify apt sources

  local logSectionDesc="Verifying Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Step 1: Verify the deb line exists
  if [[ ! -f "$apt_source_file" ]]; then
    ndr_logError "APT source file not found: [$apt_source_file]."
    return 1
  fi

  if grep -Fxq "$deb_line" "$apt_source_file"; then
    ndr_logInfo "Docker APT source entry is present."
  else
    ndr_logError "Docker APT source entry is missing or incorrect."
    return 1
  fi

  # Step 2: Update APT
  ndr_logInfo "🔄 Running apt-get update..."
  sudo apt-get update -qq
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "apt-get update failed."
    return 1
  fi

  # Step 3: Check for Docker packages
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Checking if Docker package [$pkg] is available..."
    repo_line=$(apt-cache policy "$pkg" 2>/dev/null | grep "https://download.docker.com/linux/ubuntu $codename")
    return_code=$?
    if [[ $return_code -eq 0 && -n "$repo_line" ]]; then
      ndr_logInfo "✅ Docker package [$pkg] is available from Docker's APT repository."
    else
      ndr_logError "Docker package [$pkg] not found in the expected repository."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # install docker packages
  
  local logSectionDesc="Installing Docker packages"
  ndr_logSecStart "$logSectionDesc"

  #sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Installing Docker engine package [$pkg]..."
    cmd="sudo apt-get install --assume-yes $pkg"
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to install Docker package [$pkg]."
      return 1
    else
      ndr_logInfo "✅ Docker package [$pkg] installed successfully."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


# Running Supabase locally in a Docker container
# to create build env prerequsiites, some prerequisites must be interactively installed before the scripting can proceed.
# https://supabase.com/docs/guides/self-hosting/docker
# 1. install NPM/NPX (https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)
# 2. install GIT (https://git-scm.com/downloads)
# 3. install docker (https://docs.docker.com/desktop/setup/install/windows-install/ or https://docs.docker.com/desktop/setup/install/linux-install/)
# 4. create docker account and log in (docker desktop and pulls will not function without this). With the Docker UI open, you can observe the images and containers getting created and onlined in real time.
function ndr_packagePrereqCheck ()
{
  local logSectionDesc="Checking package prerequisites"
  ndr_logSecStart "$logSectionDesc"

  ndr_checkAndInstallPrerequisites checkOnly
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Package prereq only check failed."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Usage: ndr_BuildModuleEnvFile <folder>
function ndr_BuildModuleEnvFile () 
{
  local logSectionDesc="Parsing common command line arguments"
  ndr_logSecStart "$logSectionDesc"

  if [[ $# -lt 1 ]]; then
    ndr_logError "Usage: ndr_BuildModuleEnvFile <folder>"
    return 1
  fi

  local containerFolder="$1"
  local sourceEnvFile="${gSCRIPT_HOME_DIR}/${gNDR_ENV_MASTER_FILENAME}"
  
  # Check source file
  if [[ ! -f "$sourceEnvFile" ]]; then
    ndr_logError "Source env file '$sourceEnvFile' not found."
    return 1
  fi

  # check destination folder
  local destFolder="$gSCRIPT_HOME_DIR/../${containerFolder}"
  if [[ ! -d "$destFolder" ]]; then
    ndr_logError "Destination folder '$destFolder' does not exist."
    return 1
  fi
  local destEnvFile="${destFolder}/${gNDR_ENV_MODULE_FILENAME}"
  
  local moduleEnvVars=(
  "SITE_URL" \
  "POSTGRES_PASSWORD" \
  "ANON_KEY"
  )
  
  # Empty or create output file
  rm -f "$destEnvFile"
  > "$destEnvFile"

   # Copy specified variables
  for var in "${moduleEnvVars[@]}"; do
    line=$(grep -E "^${var}=" "$sourceEnvFile" | head -n1)
    if [[ -n "$line" ]]; then
      echo "$line" >> "$destEnvFile"
    else
      ndr_logError "Warning: Variable '$var' not found in $sourceEnvFile"
      return 1
    fi
  done

  ndr_logInfo "Module environment file created at '$destEnvFile'."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_CleanupModuleEnvFile ()
{
  local logSectionDesc="Cleaning up module environment file"
  ndr_logSecStart "$logSectionDesc"

  if [[ $# -lt 1 ]]; then
    ndr_logError "Usage: ndr_CleanupModuleEnvFile <folder>"
    return 1
  fi

  local containerFolder="$1"
  local destFolder="$gSCRIPT_HOME_DIR/../${containerFolder}"
  local destEnvFile="${destFolder}/${gNDR_ENV_MODULE_FILENAME}"

  # Check destination file
  if [[ ! -f "$destEnvFile" ]]; then
    ndr_logError "Destination env file '$destEnvFile' not found."
    return 1
  fi

  # Remove the file
  rm -f "$destEnvFile"
  ndr_logInfo "Module environment file '$destEnvFile' removed."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_InstallHomeCheck ()
{
  local logSectionDesc="$gCOMPANY_NAME install home check"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" ]]; then
    # read from registry if package already present.
    local homeDir=""
    homeDir=$(ndr_getHomeDirReg)
    return_code=$?
    if [[ $return_code -eq 0 && -n "$homeDir" ]]; then
      ndr_logInfo "Found home directory entry [$homeDir] in registry."
      gNEXTDR_HOME_DIR="$homeDir"
      return 0
    fi
  fi

  if [ "$gExpressMode" -eq 1 ]; then
    if [ -z "$gNEXTDR_HOME_DIR" ]; then
      ndr_logError "gNEXTDR_HOME_DIR is not set in express mode. Please set it before proceeding."
      return 1
    fi
    
    # we should also create if it does not exist
    if [ ! -d "$gNEXTDR_HOME_DIR" ]; then
      ndr_logInfo "Creating directory [$gNEXTDR_HOME_DIR] for product and container installation."
      
      mkdir -p "$gNEXTDR_HOME_DIR"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to create directory [$gNEXTDR_HOME_DIR]. Please check permissions and try again."
        return 1
      fi
    fi

    #ndr_logInfo "Skipping install home check in express mode to directory [$gNEXTDR_HOME_DIR]."
    return 0
  fi

  echo "This will install $gCOMPANY_NAME and its Supabase Docker container under the directory [default: $gNEXTDR_HOME_DIR]."
  echo "If this is not the desired installation location, please select No and enter the correct location or select quit to return to the main menu."
  
  read -p "Are you sure you want to proceed? (Yes/no/quit) " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Qq]$ ]]; then
      ndr_logWarn "Operation cancelled."
      return 1

  elif [[ $REPLY =~ ^[Nn]$ ]]; then

    while true; do
      inputDir=""
      read -r -p "Enter the directory where you want to install the softare package: " inputDir
      if [ -z "$inputDir" ]; then
        ndr_logError "No directory entered. Please enter a valid directory."
        continue;
      fi
      
      confirm=""
      read -r -p "You entered: [$inputDir], is this correct? (Y/n)" confirm
      if [[ "$confirm" =~ ^[Nn]$ ]]; then
        ndr_logInfo "Please re-enter the directory."
        continue
      fi

      if [ ! -d "$inputDir" ]; then
        ndr_logWarn "Directory [$inputDir] does not exist, creating..."
        mkdir -p "$inputDir"
        return_code=$?
        if [ $return_code -ne 0 ]; then
          ndr_logError "Failed to create directory [$inputDir]. Please check permissions and try again."
          continue
        fi
        ndr_logInfo "Directory [$inputDir] created successfully."
      fi

      # Check if the directory is writable
      if [ ! -w "$inputDir" ]; then
        ndr_logError "Directory [$inputDir] is not writable. Please choose a different directory."
        continue
      fi

      # If we reach here, the directory is valid and writable
      ndr_logInfo "Using directory [$inputDir] for $gCOMPANY_NAME installation."
      gNEXTDR_HOME_DIR="$inputDir"
      
      break
    done
  else

    if [ ! -d "$gNEXTDR_HOME_DIR" ]; then
      ndr_logWarn "Directory [$gNEXTDR_HOME_DIR] does not exist, creating..."
      mkdir -p "$gNEXTDR_HOME_DIR"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to create directory [$gNEXTDR_HOME_DIR]. Please check permissions and try again."
        return 1
      fi
      ndr_logInfo "Directory [$gNEXTDR_HOME_DIR] created successfully."
    fi

    # Check if the directory is writable
    if [ ! -w "$gNEXTDR_HOME_DIR" ]; then
      ndr_logError "Directory [$gNEXTDR_HOME_DIR] is not writable. Please choose a different directory."
      return 1
    fi

    # If we reach here, the directory is valid and writable
    ndr_logInfo "Using directory [$gNEXTDR_HOME_DIR] for $gCOMPANY_NAME installation."

  fi
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
