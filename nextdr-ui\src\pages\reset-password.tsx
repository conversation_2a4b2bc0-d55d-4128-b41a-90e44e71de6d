import { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Shield, Eye, EyeOff, Lock, AlertCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/lib/supabase-client";
import { toast } from "@/components/ui/sonner";

const resetPasswordSchema = z
	.object({
		password: z.string().min(6, "Password must be at least 6 characters"),
		confirmPassword: z.string(),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

const ResetPassword = () => {
	const navigate = useNavigate();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<ResetPasswordFormValues>({
		resolver: zodResolver(resetPasswordSchema),
		defaultValues: {
			password: "",
			confirmPassword: "",
		},
	});

	const onSubmit = async (data: ResetPasswordFormValues) => {
		setIsSubmitting(true);

		try {
			const { error } = await supabase.auth.updateUser({
				password: data.password,
			});

			if (error) {
				toast.error(error.message);
			} else {
				toast.success("Password has been reset successfully!");
				navigate("/login");
			}
		} catch (error) {
			toast.error("An unexpected error occurred");
		}

		setIsSubmitting(false);
	};

	return (
		<div className="flex min-h-screen items-center justify-center p-4 bg-dr-dark relative overflow-hidden">
			{/* Background pattern */}
			<div className="absolute inset-0 opacity-5">
				<div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_center,rgba(29,185,84,0.1)_0,rgba(18,18,18,0)_70%)]" />
				<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200%] h-[200%] bg-[radial-gradient(ellipse_at_center,rgba(29,185,84,0.05)_0,rgba(18,18,18,0)_70%)]" />
			</div>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="w-full max-w-md z-10"
			>
				<motion.div
					className="text-center mb-8"
					initial={{ opacity: 0, scale: 0.9 }}
					animate={{ opacity: 1, scale: 1 }}
					transition={{ delay: 0.2, duration: 0.5 }}
				>
					<Shield className="h-14 w-14 text-dr-purple mx-auto mb-3" />
					<h1 className="text-3xl font-bold text-foreground">orKrestrate.AI</h1>
					<p className="text-muted-foreground mt-2">Reset Your Password</p>
				</motion.div>

				<Card className="border-muted/20 shadow-lg shadow-dr-purple/5 backdrop-blur-sm bg-card/95">
					<CardHeader className="pb-4">
						<CardTitle className="text-xl">Reset Password</CardTitle>
						<CardDescription>Enter your new password below</CardDescription>
					</CardHeader>

					<CardContent>
						<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="password" className="text-sm font-medium">
									New Password
								</Label>
								<div className="relative">
									<Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
									<Input
										id="password"
										type={showPassword ? "text" : "password"}
										{...register("password")}
										placeholder="••••••••"
										className="pl-10 pr-10"
									/>
									<button
										type="button"
										onClick={() => setShowPassword(!showPassword)}
										className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
									>
										{showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
									</button>
								</div>
								{errors.password && (
									<p className="text-destructive text-sm flex items-center gap-1 mt-1">
										<AlertCircle className="h-3 w-3" />
										{errors.password.message}
									</p>
								)}
							</div>

							<div className="space-y-2">
								<Label htmlFor="confirmPassword" className="text-sm font-medium">
									Confirm Password
								</Label>
								<div className="relative">
									<Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
									<Input
										id="confirmPassword"
										type={showConfirmPassword ? "text" : "password"}
										{...register("confirmPassword")}
										placeholder="••••••••"
										className="pl-10 pr-10"
									/>
									<button
										type="button"
										onClick={() => setShowConfirmPassword(!showConfirmPassword)}
										className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
									>
										{showConfirmPassword ? (
											<EyeOff className="h-4 w-4" />
										) : (
											<Eye className="h-4 w-4" />
										)}
									</button>
								</div>
								{errors.confirmPassword && (
									<p className="text-destructive text-sm flex items-center gap-1 mt-1">
										<AlertCircle className="h-3 w-3" />
										{errors.confirmPassword.message}
									</p>
								)}
							</div>

							<Button
								type="submit"
								className="w-full bg-dr-purple hover:bg-dr-purple-light text-black"
								disabled={isSubmitting}
							>
								{isSubmitting ? "Resetting..." : "Reset Password"}
							</Button>
						</form>
					</CardContent>

					<CardFooter className="flex justify-center">
						<Button
							variant="link"
							className="text-dr-purple hover:text-dr-purple-light"
							onClick={() => navigate("/login")}
						>
							Back to Login
						</Button>
					</CardFooter>
				</Card>
			</motion.div>
		</div>
	);
};

export default ResetPassword;
