import React, { useEffect, useState } from "react";
import { Database, Network, Server, User, Plus, Trash2 } from "lucide-react";
import { VM, ApplicationGroup, Datacenter } from "@/lib/types";
import { getVMConfig } from "@/lib/api/api-client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import AppNetworkProp from "./AppNetworkProp";
import DatabaseStorageDiscovery from "./DatabaseStorageDiscovery";
import GoogleCloudIAMDiscovery from "./GoogleCloudIAMDiscovery";

interface AppMenuProps {
	selectedGroup: ApplicationGroup;
	handleRemoveVM: (groupId: string, vmId: string) => Promise<void>;
	onAddVM: () => void;
	isLoadingInstances: boolean;
	datacenterId: string;
	id: string;
}

function AppMenu({
	selectedGroup,
	handleRemoveVM,
	onAddVM,
	isLoadingInstances,
	datacenterId,
	id,
}: AppMenuProps) {
	const [instances, setInstances] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);
	const { toast } = useToast();

	useEffect(() => {
		async function fetchVMs() {
			if (!selectedGroup?.vm_ids?.length) {
				setInstances([]);
				return;
			}

			setLoading(true);
			try {
				const instancesData = [];
				for (const vmId of selectedGroup.vm_ids) {
					try {
						const vmData = await getVMConfig(selectedGroup.data_center_id, vmId, id);
						instancesData.push(vmData);
					} catch (error) {
						console.error(`Error fetching VM ${vmId}:`, error);
					}
				}
				setInstances(instancesData);
			} catch (error) {
				console.error("Error fetching VMs:", error);
				toast({
					title: "Error",
					description: "Failed to load VM instances",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		}

		fetchVMs();
	}, [selectedGroup?.vm_ids, selectedGroup?.data_center_id, id]);

	return (
		<Tabs defaultValue="compute" className="w-full">
			<TabsList className="grid grid-cols-4 mb-6">
				<TabsTrigger value="compute">Compute</TabsTrigger>
				<TabsTrigger value="networking">Networking</TabsTrigger>
				<TabsTrigger value="databases">Databases and Storage</TabsTrigger>
				<TabsTrigger value="accounts">Accounts and Roles</TabsTrigger>
			</TabsList>

			<TabsContent value="compute" className="space-y-4">
				<div className="flex justify-between mb-2">
					<h3 className="text-lg font-medium flex items-center gap-2">
						<Server className="h-5 w-5 text-blue-500" />
						Compute Instances
					</h3>
					<Button variant="outline" size="sm" onClick={onAddVM}>
						<Plus className="mr-1 h-4 w-4" />
						Add VM
					</Button>
				</div>

				{loading || isLoadingInstances ? (
					<div className="flex justify-center items-center py-8">
						<div className="flex flex-col items-center gap-2">
							<div className="animate-spin mr-2 h-6 w-6 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
							<span className="text-sm text-muted-foreground">
								{loading ? "Loading VM details..." : "Refreshing VM list..."}
							</span>
						</div>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{instances.length === 0 ? (
							<div className="col-span-full text-center py-8">
								<Server className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
								<h4 className="text-lg font-medium mb-2">No Compute Resources</h4>
								<p className="text-muted-foreground mb-4">
									This project has no compute resources configured
								</p>
								<Button className="bg-dr-purple hover:bg-dr-purple-dark" onClick={onAddVM}>
									<Plus className="mr-2 h-4 w-4" />
									Add Instance
								</Button>
							</div>
						) : (
							<>
								{instances.map((vm) => (
									<Card
										key={vm.id}
										className="overflow-hidden border border-border hover:border-dr-purple transition-all duration-200"
									>
										<div className="p-3 border-b border-border">
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-2">
													<div>
														<Server
															className={`h-4 w-4 ${
																vm.status === "RUNNING"
																	? "text-green-500"
																	: vm.status === "STOPPED"
																	? "text-red-500"
																	: "text-yellow-500"
															}`}
														/>
													</div>
													<h3 className="font-semibold text-sm">{vm.name}</h3>
												</div>

												<div className="flex items-center gap-2">
													<button
														onClick={() => handleRemoveVM(selectedGroup.id.toString(), vm.id)}
														className="p-2 rounded-md bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
														title="Remove VM"
													>
														<Trash2 className="h-4 w-4" />
													</button>
												</div>
											</div>
										</div>

										<CardContent className="p-4">
											<div className="flex flex-col gap-2 text-sm">
												<div className="flex items-center justify-between">
													<span className="text-muted-foreground">Status</span>
													<span
														className={`px-2 py-0.5 rounded-full text-xs flex items-center gap-1 ${
															vm.status === "RUNNING"
																? "bg-green-900/30 text-green-400"
																: vm.status === "STOPPED"
																? "bg-red-900/30 text-red-400"
																: "bg-yellow-900/30 text-yellow-400"
														}`}
													>
														{vm.status === "RUNNING" && (
															<span className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></span>
														)}
														{vm.status === "STOPPED" && (
															<span className="w-1.5 h-1.5 bg-red-400 rounded-full"></span>
														)}
														{vm.status !== "RUNNING" && vm.status !== "STOPPED" && (
															<span className="w-1.5 h-1.5 bg-yellow-400 rounded-full"></span>
														)}
														{vm.status}
													</span>
												</div>

												<div className="flex items-center justify-between">
													<span className="text-muted-foreground">Project ID</span>
													<span className="font-mono text-xs bg-secondary px-2 py-0.5 rounded">
														{selectedGroup?.data_center_id}
													</span>
												</div>
											</div>
										</CardContent>
									</Card>
								))}

								<Card
									onClick={onAddVM}
									className="overflow-hidden border border-border hover:border-dr-purple transition-all duration-200 cursor-pointer"
								>
									<div className="p-3 border-b border-border">
										<div className="flex items-center">
											<div className="flex items-center gap-2">
												<div>
													<Plus className="h-4 w-4 text-blue-400" />
												</div>
												<h3 className="font-semibold text-sm">Add Instance</h3>
											</div>
										</div>
									</div>

									<CardContent className="p-4">
										<div className="flex flex-col gap-2 text-sm">
											<div className="flex items-center justify-center py-2">
												<span className="text-xs text-muted-foreground">
													Add a new instance to this group
												</span>
											</div>
											<div className="flex items-center justify-center">
												<span className="bg-blue-900/30 text-blue-400 px-2 py-0.5 rounded-full text-xs flex items-center gap-1">
													<span className="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
													Configure new VM
												</span>
											</div>
										</div>
									</CardContent>
								</Card>
							</>
						)}
					</div>
				)}
			</TabsContent>

			<TabsContent value="networking" className="space-y-4">
				<AppNetworkProp projectId={selectedGroup?.data_center_id} datacenterId={id} />
			</TabsContent>

			<TabsContent value="databases" className="space-y-4">
				<DatabaseStorageDiscovery projectId={selectedGroup?.data_center_id} datacenterId={id} />
			</TabsContent>

			<TabsContent value="accounts" className="space-y-4">
				<GoogleCloudIAMDiscovery projectId={selectedGroup?.data_center_id} datacenterId={id} />
			</TabsContent>
		</Tabs>
	);
}

export default AppMenu;
