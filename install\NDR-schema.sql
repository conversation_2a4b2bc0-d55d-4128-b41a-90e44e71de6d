

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."pricing_plan_interval" AS ENUM (
    'day',
    'week',
    'month',
    'year'
);


ALTER TYPE "public"."pricing_plan_interval" OWNER TO "postgres";


CREATE TYPE "public"."pricing_type" AS ENUM (
    'one_time',
    'recurring'
);


ALTER TYPE "public"."pricing_type" OWNER TO "postgres";


CREATE TYPE "public"."subscription_status" AS ENUM (
    'trialing',
    'active',
    'canceled',
    'incomplete',
    'incomplete_expired',
    'past_due',
    'unpaid'
);


ALTER TYPE "public"."subscription_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_step_approval"("step_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM recovery_steps_new
        WHERE id = step_id
        AND requires_approval = true
        AND (
            approval_metadata->>'approval_status' IS NULL
            OR approval_metadata->>'approval_status' = 'pending'
        )
    );
END;
$$;


ALTER FUNCTION "public"."check_step_approval"("step_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."ensure_user_profiles"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    INSERT INTO user_profiles (id, email, role)
    SELECT id, email, 'user'
    FROM auth.users u
    WHERE NOT EXISTS (
        SELECT 1 FROM user_profiles p WHERE p.id = u.id
    );
END;
$$;


ALTER FUNCTION "public"."ensure_user_profiles"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Check if profile already exists
    IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE id = NEW.id) THEN
        INSERT INTO public.user_profiles (id, email, role)
        VALUES (NEW.id, NEW.email, 'user');
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_user_deletion"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Log the deletion in an audit table if needed
    -- For now, we'll just let the CASCADE handle the user_profiles deletion
    RETURN OLD;
END;
$$;


ALTER FUNCTION "public"."handle_user_deletion"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = $1 AND role = 'admin'
  );
END;
$_$;


ALTER FUNCTION "public"."is_admin"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_user_action"("action" "text", "entity_id" "uuid", "details" "jsonb" DEFAULT '{}'::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO audit_logs (
        user_id,
        action,
        entity_type,
        entity_id,
        details
    )
    VALUES (
        auth.uid(),
        action,
        'user',
        entity_id,
        details
    )
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$;


ALTER FUNCTION "public"."log_user_action"("action" "text", "entity_id" "uuid", "details" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_user_profile_deletion"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    PERFORM public.log_user_action(
        'user_deleted',
        OLD.id,
        jsonb_build_object(
            'email', OLD.email,
            'role', OLD.role,
            'status', OLD.status
        )
    );
    RETURN OLD;
END;
$$;


ALTER FUNCTION "public"."log_user_profile_deletion"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_user_profile_update"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    IF OLD.role != NEW.role THEN
        PERFORM public.log_user_action(
            'role_update',
            NEW.id,
            jsonb_build_object(
                'old_role', OLD.role,
                'new_role', NEW.role
            )
        );
    END IF;

    IF OLD.status != NEW.status THEN
        PERFORM public.log_user_action(
            'status_update',
            NEW.id,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status
            )
        );
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."log_user_profile_update"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."resolve_step_assignees"("step_id" "uuid") RETURNS TABLE("user_id" "uuid", "email" "text", "role" "text", "status" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH step_info AS (
        SELECT assignee_type, assignee_id
        FROM recovery_steps_new
        WHERE id = step_id
    )
    SELECT 
        u.id as user_id,
        u.email,
        p.role,
        p.status
    FROM step_info s
    LEFT JOIN LATERAL (
        SELECT id, email, role, status
        FROM user_profiles
        WHERE s.assignee_type = 'user' AND id = s.assignee_id
        UNION ALL
        SELECT up.id, up.email, up.role, up.status
        FROM group_members gm
        JOIN user_profiles up ON up.id = gm.user_id
        WHERE s.assignee_type = 'group' AND gm.group_id = s.assignee_id
    ) u ON true;
END;
$$;


ALTER FUNCTION "public"."resolve_step_assignees"("step_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_modified_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_modified_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_role"("user_id" "uuid", "new_role" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Only admins can update user roles';
    END IF;

    -- Update the user's role
    UPDATE user_profiles
    SET role = new_role,
        updated_at = NOW()
    WHERE id = user_id;

    -- Update the user's custom claims
    UPDATE auth.users
    SET raw_user_meta_data = jsonb_set(
        COALESCE(raw_user_meta_data, '{}'::jsonb),
        '{role}',
        to_jsonb(new_role)
    )
    WHERE id = user_id;
END;
$$;


ALTER FUNCTION "public"."update_user_role"("user_id" "uuid", "new_role" "text") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."activity_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "action" "text" NOT NULL,
    "entity_type" "text" NOT NULL,
    "entity_id" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."activity_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."agent_commands" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "command" "text"
);


ALTER TABLE "public"."agent_commands" OWNER TO "postgres";


COMMENT ON COLUMN "public"."agent_commands"."command" IS 'agent command';



ALTER TABLE "public"."agent_commands" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."agent_commands_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."agent_response" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "response" "text",
    "command_id" bigint
);


ALTER TABLE "public"."agent_response" OWNER TO "postgres";


COMMENT ON COLUMN "public"."agent_response"."command_id" IS 'id from source command from agent_commands table';



ALTER TABLE "public"."agent_response" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."agent_response_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."application_groups" (
    "id" bigint NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "vm_ids" "text"[],
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "data_center_id" "text"
);


ALTER TABLE "public"."application_groups" OWNER TO "postgres";


COMMENT ON TABLE "public"."application_groups" IS 'This is a duplicate of applications';



ALTER TABLE "public"."application_groups" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."application_groups_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."applications" (
    "id" bigint NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "vm_ids" "text"[],
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "backupcatalog" bigint
);


ALTER TABLE "public"."applications" OWNER TO "postgres";


COMMENT ON COLUMN "public"."applications"."backupcatalog" IS 'link to backup catalog';



ALTER TABLE "public"."applications" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."applications_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."approval_tokens" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "step_id" "uuid" NOT NULL,
    "approver_id" "uuid" NOT NULL,
    "token" "text" NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "is_used" boolean DEFAULT false,
    "used_at" timestamp with time zone,
    "used_by" "uuid",
    "is_checkpoint" boolean DEFAULT false,
    "checkpoint_id" "uuid"
);


ALTER TABLE "public"."approval_tokens" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."audit_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "action" "text" NOT NULL,
    "entity_type" "text" NOT NULL,
    "entity_id" "uuid",
    "details" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."audit_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."backup_execution" (
    "execution_id" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "schedule_id" "text" DEFAULT ''::"text" NOT NULL,
    "project_config_snapshot" "jsonb" NOT NULL,
    "vm_snapshots" "jsonb",
    "status" "text" DEFAULT ''::"text" NOT NULL
);


ALTER TABLE "public"."backup_execution" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."backupcatalogs" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "hostname" "text",
    "username" "text",
    "password" "text",
    "port" "text",
    "apitoken" "text",
    "hypervisor_type" "text",
    "user_id" "uuid" DEFAULT "auth"."uid"()
);


ALTER TABLE "public"."backupcatalogs" OWNER TO "postgres";


COMMENT ON TABLE "public"."backupcatalogs" IS 'Table for backup catalogs like Cohesity, CommVault, Rubrik';



ALTER TABLE "public"."backupcatalogs" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."backupcatalogs_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."customers" (
    "id" "uuid" NOT NULL,
    "stripe_customer_id" "text"
);


ALTER TABLE "public"."customers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."datacenters" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "hostname" "text",
    "username" "text",
    "password" "text",
    "port" "text",
    "apitoken" "text",
    "hypervisor_type" "text"
);


ALTER TABLE "public"."datacenters" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."datacenters2" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "hypervisor_type" "text",
    "apitoken" "json",
    "project_id" "text"
);


ALTER TABLE "public"."datacenters2" OWNER TO "postgres";


ALTER TABLE "public"."datacenters2" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."datacenters2_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE "public"."datacenters" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."datacenters_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."disks" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "vm_id" bigint,
    "name" character varying(255) NOT NULL,
    "type" character varying(50) NOT NULL,
    "size_gb" integer NOT NULL,
    "recovery_points" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "disks_type_check" CHECK ((("type")::"text" = ANY (ARRAY[('os'::character varying)::"text", ('data'::character varying)::"text"])))
);


ALTER TABLE "public"."disks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."group_members" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "group_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "text" DEFAULT 'member'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "created_by" "uuid",
    CONSTRAINT "group_members_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'member'::"text"])))
);


ALTER TABLE "public"."group_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."integration_configs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "source" "text" NOT NULL,
    "enabled" boolean DEFAULT false NOT NULL,
    "last_sync_at" timestamp with time zone,
    "sync_status" "text",
    "error_message" "text",
    "config" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "integration_configs_source_check" CHECK (("source" = ANY (ARRAY['native'::"text", 'gcp_ad'::"text"]))),
    CONSTRAINT "integration_configs_sync_status_check" CHECK (("sync_status" = ANY (ARRAY['idle'::"text", 'syncing'::"text", 'success'::"text", 'error'::"text", 'in_progress'::"text"])))
);


ALTER TABLE "public"."integration_configs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."internal_groups" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "type" "text" DEFAULT 'internal'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "created_by" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "internal_groups_type_check" CHECK (("type" = 'internal'::"text"))
);


ALTER TABLE "public"."internal_groups" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."licenses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "customer_id" "text" NOT NULL,
    "jwt" "text" NOT NULL,
    "issued_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."licenses" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."liked_songs" (
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "song_id" bigint
);


ALTER TABLE "public"."liked_songs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prices" (
    "id" "text" NOT NULL,
    "product_id" "text",
    "active" boolean,
    "description" "text",
    "unit_amount" bigint,
    "currency" "text",
    "type" "public"."pricing_type",
    "interval" "public"."pricing_plan_interval",
    "interval_count" integer,
    "trial_period_days" integer,
    "metadata" "jsonb",
    CONSTRAINT "prices_currency_check" CHECK (("char_length"("currency") = 3))
);


ALTER TABLE "public"."prices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "text" NOT NULL,
    "active" boolean,
    "name" "text",
    "description" "text",
    "image" "text",
    "metadata" "jsonb"
);


ALTER TABLE "public"."products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_plan_audit_log" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "recovery_plan_id" "uuid" NOT NULL,
    "step_id" "uuid" NOT NULL,
    "action" "text" NOT NULL,
    "performed_by" "uuid" NOT NULL,
    "details" "jsonb",
    "ip_address" "text",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."recovery_plan_audit_log" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_plan_checkpoints" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "recovery_plan_id" "uuid" NOT NULL,
    "step_id" "uuid" NOT NULL,
    "approver_id" "uuid",
    "approver_role" "text",
    "approval_required" boolean DEFAULT true NOT NULL,
    "approval_status" "text",
    "approved_at" timestamp with time zone,
    "approved_by" "uuid",
    "approval_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "recovery_plan_checkpoints_approval_status_check" CHECK (("approval_status" = ANY (ARRAY['pending'::"text", 'approved'::"text", 'rejected'::"text"]))),
    CONSTRAINT "recovery_plan_checkpoints_check" CHECK (((("approver_id" IS NOT NULL) AND ("approver_role" IS NULL)) OR (("approver_id" IS NULL) AND ("approver_role" IS NOT NULL))))
);


ALTER TABLE "public"."recovery_plan_checkpoints" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_plan_progress" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "recovery_plan_id" "uuid" NOT NULL,
    "step_id" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "started_at" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "execution_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "recovery_plan_progress_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'in_progress'::"text", 'completed'::"text", 'failed'::"text", 'awaiting_approval'::"text", 'approved'::"text", 'rejected'::"text"])))
);


ALTER TABLE "public"."recovery_plan_progress" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_plans" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "app_group_id" bigint,
    "created_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."recovery_plans" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_plans_new" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "app_group_id" bigint,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "status" "text",
    "created_by" "uuid",
    "current_execution_id" "uuid",
    "execution_status" "text",
    "execution_started_at" timestamp with time zone,
    "execution_completed_at" timestamp with time zone,
    "execution_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "recovery_plans_new_execution_status_check" CHECK (("execution_status" = ANY (ARRAY['not_started'::"text", 'in_progress'::"text", 'paused'::"text", 'completed'::"text", 'failed'::"text"])))
);


ALTER TABLE "public"."recovery_plans_new" OWNER TO "postgres";


COMMENT ON TABLE "public"."recovery_plans_new" IS 'This is a duplicate of recovery_plans';



CREATE TABLE IF NOT EXISTS "public"."recovery_steps" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "recovery_plan_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "status" "text" DEFAULT 'Pending'::"text",
    "owner" "text",
    "operation_type" "text",
    "step_order" integer NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"(),
    CONSTRAINT "recovery_steps_status_check" CHECK (("status" = ANY (ARRAY['Pending'::"text", 'In Progress'::"text", 'Completed'::"text"])))
);


ALTER TABLE "public"."recovery_steps" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recovery_steps_new" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "recovery_plan_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "status" "text" DEFAULT 'Pending'::"text",
    "owner" "text",
    "operation_type" "text",
    "step_order" integer NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "configuration" "jsonb",
    "assignee_type" "text",
    "assignee_id" "uuid",
    "assignee_name" "text",
    "assignee_details" "jsonb" DEFAULT '{}'::"jsonb",
    "requires_approval" boolean DEFAULT false,
    "approval_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "recovery_steps_new_assignee_type_check" CHECK (("assignee_type" = ANY (ARRAY['user'::"text", 'group'::"text"]))),
    CONSTRAINT "recovery_steps_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'in_progress'::"text", 'completed'::"text", 'failed'::"text", 'awaiting_approval'::"text"])))
);


ALTER TABLE "public"."recovery_steps_new" OWNER TO "postgres";


COMMENT ON TABLE "public"."recovery_steps_new" IS 'This is a duplicate of recovery_steps';



CREATE TABLE IF NOT EXISTS "public"."snapshot_schedules" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "group_id" "text" NOT NULL,
    "vm_ids" "text"[] NOT NULL,
    "frequency" "text" NOT NULL,
    "retention_period" smallint NOT NULL,
    "start_time" "text",
    "day_of_week" smallint,
    "day_of_month" smallint,
    "status" "text" DEFAULT '''active'''::"text",
    "next_run" timestamp with time zone,
    "last_run" timestamp with time zone,
    "datacenterId" bigint
);


ALTER TABLE "public"."snapshot_schedules" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."songs" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "title" "text",
    "song_path" "text",
    "image_path" "text",
    "author" "text",
    "user_id" "uuid"
);


ALTER TABLE "public"."songs" OWNER TO "postgres";


ALTER TABLE "public"."songs" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."songs_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."subscriptions" (
    "id" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "status" "public"."subscription_status",
    "metadata" "jsonb",
    "price_id" "text",
    "quantity" integer,
    "cancel_at_period_end" boolean,
    "created" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "current_period_start" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "current_period_end" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "ended_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "cancel_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "canceled_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "trial_start" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "trial_end" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."subscriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sync_stats" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "source" "text" NOT NULL,
    "total_users" integer DEFAULT 0 NOT NULL,
    "total_groups" integer DEFAULT 0 NOT NULL,
    "synced_users" integer DEFAULT 0 NOT NULL,
    "synced_groups" integer DEFAULT 0 NOT NULL,
    "conflicts" integer DEFAULT 0 NOT NULL,
    "last_sync_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "sync_stats_source_check" CHECK (("source" = ANY (ARRAY['native'::"text", 'gcp_ad'::"text"])))
);


ALTER TABLE "public"."sync_stats" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tasktable" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "task" "text",
    "status" "text",
    "response" "text"
);


ALTER TABLE "public"."tasktable" OWNER TO "postgres";


ALTER TABLE "public"."tasktable" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."tasktable_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."user_profiles" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "role" "text" NOT NULL,
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "last_sign_in_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "source" "text" DEFAULT 'native'::"text" NOT NULL,
    "sync_metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "user_profiles_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'operator'::"text", 'approver'::"text", 'viewer'::"text"]))),
    CONSTRAINT "user_profiles_source_check" CHECK (("source" = ANY (ARRAY['native'::"text", 'gcp_ad'::"text"]))),
    CONSTRAINT "user_profiles_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'invited'::"text", 'suspended'::"text"])))
);


ALTER TABLE "public"."user_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "full_name" "text",
    "avatar_url" "text",
    "billing_address" "jsonb",
    "payment_method" "jsonb"
);


ALTER TABLE "public"."users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vmconfig" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "machine_type" character varying DEFAULT ''::character varying,
    "zone" character varying DEFAULT ''::character varying,
    "status" character varying DEFAULT ''::character varying,
    "network_interfaces" "jsonb",
    "disks" "jsonb",
    "iam_policies" "jsonb",
    "name" "text" DEFAULT ''::"text"
);


ALTER TABLE "public"."vmconfig" OWNER TO "postgres";


ALTER TABLE "public"."vmconfig" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vmconfig_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vms" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text",
    "datacenter_id" bigint,
    "vm_id" "text",
    "power_state" "text",
    "cpu_count" "text",
    "memory_size_mb" "text"
);


ALTER TABLE "public"."vms" OWNER TO "postgres";


ALTER TABLE "public"."vms" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vms_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."agent_commands"
    ADD CONSTRAINT "agent_commands_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."agent_response"
    ADD CONSTRAINT "agent_response_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."application_groups"
    ADD CONSTRAINT "application_groups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."applications"
    ADD CONSTRAINT "applications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."backup_execution"
    ADD CONSTRAINT "backup_execution_pkey" PRIMARY KEY ("execution_id");



ALTER TABLE ONLY "public"."backupcatalogs"
    ADD CONSTRAINT "backupcatalogs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."datacenters2"
    ADD CONSTRAINT "datacenters2_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."datacenters2"
    ADD CONSTRAINT "datacenters2_project_id_key" UNIQUE ("project_id");



ALTER TABLE ONLY "public"."datacenters"
    ADD CONSTRAINT "datacenters_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."disks"
    ADD CONSTRAINT "disks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."group_members"
    ADD CONSTRAINT "group_members_group_id_user_id_key" UNIQUE ("group_id", "user_id");



ALTER TABLE ONLY "public"."group_members"
    ADD CONSTRAINT "group_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."integration_configs"
    ADD CONSTRAINT "integration_configs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."integration_configs"
    ADD CONSTRAINT "integration_configs_source_unique" UNIQUE ("source");



ALTER TABLE ONLY "public"."internal_groups"
    ADD CONSTRAINT "internal_groups_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."internal_groups"
    ADD CONSTRAINT "internal_groups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."licenses"
    ADD CONSTRAINT "licenses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."liked_songs"
    ADD CONSTRAINT "liked_songs_pkey" PRIMARY KEY ("user_id", "created_at");



ALTER TABLE ONLY "public"."prices"
    ADD CONSTRAINT "prices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "recovery_plan_audit_log_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_recovery_plan_id_step_id_key" UNIQUE ("recovery_plan_id", "step_id");



ALTER TABLE ONLY "public"."recovery_plan_progress"
    ADD CONSTRAINT "recovery_plan_progress_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_plan_progress"
    ADD CONSTRAINT "recovery_plan_progress_recovery_plan_id_step_id_key" UNIQUE ("recovery_plan_id", "step_id");



ALTER TABLE ONLY "public"."recovery_plans_new"
    ADD CONSTRAINT "recovery_plans_new_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_plans"
    ADD CONSTRAINT "recovery_plans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_steps_new"
    ADD CONSTRAINT "recovery_steps_new_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recovery_steps"
    ADD CONSTRAINT "recovery_steps_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."snapshot_schedules"
    ADD CONSTRAINT "snapshots_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."songs"
    ADD CONSTRAINT "songs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sync_stats"
    ADD CONSTRAINT "sync_stats_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sync_stats"
    ADD CONSTRAINT "sync_stats_source_unique" UNIQUE ("source");



ALTER TABLE ONLY "public"."tasktable"
    ADD CONSTRAINT "tasktable_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vmconfig"
    ADD CONSTRAINT "vmconfig_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vms"
    ADD CONSTRAINT "vms_pkey" PRIMARY KEY ("id");



CREATE INDEX "activity_logs_action_idx" ON "public"."activity_logs" USING "btree" ("action");



CREATE INDEX "activity_logs_created_at_idx" ON "public"."activity_logs" USING "btree" ("created_at");



CREATE INDEX "activity_logs_entity_type_idx" ON "public"."activity_logs" USING "btree" ("entity_type");



CREATE INDEX "activity_logs_user_id_idx" ON "public"."activity_logs" USING "btree" ("user_id");



CREATE INDEX "idx_approval_tokens_approver" ON "public"."approval_tokens" USING "btree" ("approver_id");



CREATE INDEX "idx_approval_tokens_checkpoint" ON "public"."approval_tokens" USING "btree" ("checkpoint_id");



CREATE INDEX "idx_approval_tokens_step" ON "public"."approval_tokens" USING "btree" ("step_id");



CREATE INDEX "idx_approval_tokens_token" ON "public"."approval_tokens" USING "btree" ("token");



CREATE INDEX "idx_disks_vm_id" ON "public"."disks" USING "btree" ("vm_id");



CREATE INDEX "idx_group_members_group_id" ON "public"."group_members" USING "btree" ("group_id");



CREATE INDEX "idx_group_members_user_id" ON "public"."group_members" USING "btree" ("user_id");



CREATE INDEX "idx_integration_configs_source" ON "public"."integration_configs" USING "btree" ("source");



CREATE INDEX "idx_recovery_plan_audit_log_performed_by" ON "public"."recovery_plan_audit_log" USING "btree" ("performed_by");



CREATE INDEX "idx_recovery_plan_audit_log_plan" ON "public"."recovery_plan_audit_log" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_recovery_plan_audit_log_step" ON "public"."recovery_plan_audit_log" USING "btree" ("step_id");



CREATE INDEX "idx_recovery_plan_checkpoints_approver" ON "public"."recovery_plan_checkpoints" USING "btree" ("approver_id");



CREATE INDEX "idx_recovery_plan_checkpoints_plan" ON "public"."recovery_plan_checkpoints" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_recovery_plan_checkpoints_recovery_plan_id" ON "public"."recovery_plan_checkpoints" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_recovery_plan_checkpoints_role" ON "public"."recovery_plan_checkpoints" USING "btree" ("approver_role");



CREATE INDEX "idx_recovery_plan_checkpoints_step" ON "public"."recovery_plan_checkpoints" USING "btree" ("step_id");



CREATE INDEX "idx_recovery_plan_progress_plan" ON "public"."recovery_plan_progress" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_recovery_plan_progress_recovery_plan_id" ON "public"."recovery_plan_progress" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_recovery_plan_progress_status" ON "public"."recovery_plan_progress" USING "btree" ("status");



CREATE INDEX "idx_recovery_plan_progress_step" ON "public"."recovery_plan_progress" USING "btree" ("step_id");



CREATE INDEX "idx_recovery_steps_assignee" ON "public"."recovery_steps_new" USING "btree" ("assignee_type", "assignee_id");



CREATE INDEX "idx_recovery_steps_recovery_plan_id" ON "public"."recovery_steps_new" USING "btree" ("recovery_plan_id");



CREATE INDEX "idx_sync_stats_source" ON "public"."sync_stats" USING "btree" ("source");



CREATE INDEX "idx_user_profiles_source" ON "public"."user_profiles" USING "btree" ("source");



CREATE INDEX "licenses_customer_id_idx" ON "public"."licenses" USING "btree" ("customer_id");



CREATE OR REPLACE TRIGGER "on_user_profile_deletion" BEFORE DELETE ON "public"."user_profiles" FOR EACH ROW EXECUTE FUNCTION "public"."log_user_profile_deletion"();



CREATE OR REPLACE TRIGGER "on_user_profile_update" AFTER UPDATE ON "public"."user_profiles" FOR EACH ROW EXECUTE FUNCTION "public"."log_user_profile_update"();



CREATE OR REPLACE TRIGGER "update_applications_updated_at" BEFORE UPDATE ON "public"."applications" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_disks_modtime" BEFORE UPDATE ON "public"."disks" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_group_members_updated_at" BEFORE UPDATE ON "public"."group_members" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_integration_configs_updated_at" BEFORE UPDATE ON "public"."integration_configs" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_internal_groups_updated_at" BEFORE UPDATE ON "public"."internal_groups" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_sync_stats_updated_at" BEFORE UPDATE ON "public"."sync_stats" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_vms_updated_at" BEFORE UPDATE ON "public"."vms" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."application_groups"
    ADD CONSTRAINT "application_groups_data_center_id_fkey" FOREIGN KEY ("data_center_id") REFERENCES "public"."datacenters2"("project_id");



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_approver_id_fkey" FOREIGN KEY ("approver_id") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_checkpoint_id_fkey" FOREIGN KEY ("checkpoint_id") REFERENCES "public"."recovery_plan_checkpoints"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_step_id_fkey" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "approval_tokens_used_by_fkey" FOREIGN KEY ("used_by") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."disks"
    ADD CONSTRAINT "disks_vm_id_fkey" FOREIGN KEY ("vm_id") REFERENCES "public"."vms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "fk_approver" FOREIGN KEY ("approver_id") REFERENCES "public"."user_profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "fk_performed_by" FOREIGN KEY ("performed_by") REFERENCES "public"."user_profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "fk_recovery_plan" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "fk_step" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."approval_tokens"
    ADD CONSTRAINT "fk_step" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."group_members"
    ADD CONSTRAINT "group_members_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."group_members"
    ADD CONSTRAINT "group_members_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."internal_groups"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."group_members"
    ADD CONSTRAINT "group_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."internal_groups"
    ADD CONSTRAINT "internal_groups_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."liked_songs"
    ADD CONSTRAINT "liked_songs_song_id_fkey" FOREIGN KEY ("song_id") REFERENCES "public"."songs"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."liked_songs"
    ADD CONSTRAINT "liked_songs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."prices"
    ADD CONSTRAINT "prices_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id");



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "recovery_plan_audit_log_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "recovery_plan_audit_log_recovery_plan_id_fkey" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans_new"("id");



ALTER TABLE ONLY "public"."recovery_plan_audit_log"
    ADD CONSTRAINT "recovery_plan_audit_log_step_id_fkey" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id");



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_approved_by_fkey" FOREIGN KEY ("approved_by") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_approver_id_fkey" FOREIGN KEY ("approver_id") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_recovery_plan_id_fkey" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_checkpoints"
    ADD CONSTRAINT "recovery_plan_checkpoints_step_id_fkey" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_progress"
    ADD CONSTRAINT "recovery_plan_progress_recovery_plan_id_fkey" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plan_progress"
    ADD CONSTRAINT "recovery_plan_progress_step_id_fkey" FOREIGN KEY ("step_id") REFERENCES "public"."recovery_steps_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plans"
    ADD CONSTRAINT "recovery_plans_app_group_id_fkey" FOREIGN KEY ("app_group_id") REFERENCES "public"."applications"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_plans_new"
    ADD CONSTRAINT "recovery_plans_new_app_group_id_fkey" FOREIGN KEY ("app_group_id") REFERENCES "public"."application_groups"("id");



ALTER TABLE ONLY "public"."recovery_plans_new"
    ADD CONSTRAINT "recovery_plans_new_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."user_profiles"("id");



ALTER TABLE ONLY "public"."recovery_steps_new"
    ADD CONSTRAINT "recovery_steps_new_recovery_plan_id_fkey" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans_new"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recovery_steps"
    ADD CONSTRAINT "recovery_steps_recovery_plan_id_fkey" FOREIGN KEY ("recovery_plan_id") REFERENCES "public"."recovery_plans"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."snapshot_schedules"
    ADD CONSTRAINT "snapshot_schedules_datacenterId_fkey" FOREIGN KEY ("datacenterId") REFERENCES "public"."datacenters2"("id");



ALTER TABLE ONLY "public"."songs"
    ADD CONSTRAINT "songs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_price_id_fkey" FOREIGN KEY ("price_id") REFERENCES "public"."prices"("id");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



CREATE POLICY "Admins and operators can view all audit logs" ON "public"."audit_logs" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = ANY (ARRAY['admin'::"text", 'operator'::"text"]))))));



CREATE POLICY "Admins can delete profiles" ON "public"."user_profiles" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles" "user_profiles_1"
  WHERE (("user_profiles_1"."id" = "auth"."uid"()) AND ("user_profiles_1"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage integration configs" ON "public"."integration_configs" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can update profiles" ON "public"."user_profiles" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles" "user_profiles_1"
  WHERE (("user_profiles_1"."id" = "auth"."uid"()) AND ("user_profiles_1"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all audit logs" ON "public"."audit_logs" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all profiles" ON "public"."user_profiles" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles" "user_profiles_1"
  WHERE (("user_profiles_1"."id" = "auth"."uid"()) AND ("user_profiles_1"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view sync stats" ON "public"."sync_stats" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow authenticated users full access to VMs" ON "public"."vms" TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text")) WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow authenticated users to read" ON "public"."applications" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow authenticated users to write" ON "public"."applications" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Authenticated users can insert logs" ON "public"."activity_logs" FOR INSERT WITH CHECK (("auth"."uid"() IS NOT NULL));



CREATE POLICY "Can only view own subs data." ON "public"."subscriptions" FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Can update own user data." ON "public"."users" FOR UPDATE USING ((( SELECT "auth"."uid"() AS "uid") = "id"));



CREATE POLICY "Can view own user data." ON "public"."users" FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "id"));



CREATE POLICY "DELETE" ON "public"."recovery_plans_new" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "DELETE" ON "public"."recovery_steps_new" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "Delete" ON "public"."application_groups" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "Delete" ON "public"."datacenters2" FOR DELETE USING (true);



CREATE POLICY "Delete" ON "public"."snapshot_schedules" FOR DELETE USING (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."application_groups" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."datacenters" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."datacenters2" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."recovery_plans" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."recovery_plans_new" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."recovery_steps" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."recovery_steps_new" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."snapshot_schedules" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."songs" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable read access for all users" ON "public"."recovery_plans_new" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."recovery_steps_new" FOR SELECT USING (true);



CREATE POLICY "Enable read access for only auth users" ON "public"."application_groups" FOR SELECT TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable read access for only auth users" ON "public"."datacenters" TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable read access for only auth users" ON "public"."datacenters2" FOR SELECT USING (true);



CREATE POLICY "Enable read access for only auth users" ON "public"."recovery_plans" FOR SELECT TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable read access for only auth users" ON "public"."recovery_steps" FOR SELECT TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Enable read access for only auth users" ON "public"."snapshot_schedules" FOR SELECT TO "authenticated" USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Insert" ON "public"."licenses" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Only admins can create groups" ON "public"."internal_groups" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can delete groups" ON "public"."internal_groups" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can manage group members" ON "public"."group_members" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text"))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can update groups" ON "public"."internal_groups" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text")))));



CREATE POLICY "READ" ON "public"."licenses" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Update" ON "public"."recovery_plans_new" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Update" ON "public"."recovery_steps_new" FOR UPDATE USING (true) WITH CHECK (true);



CREATE POLICY "Update" ON "public"."snapshot_schedules" FOR UPDATE USING (true) WITH CHECK (true);



CREATE POLICY "Update policy" ON "public"."application_groups" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Users can create approval tokens" ON "public"."approval_tokens" FOR INSERT WITH CHECK (("approver_id" = "auth"."uid"()));



CREATE POLICY "Users can delete only their own entries" ON "public"."backupcatalogs" FOR DELETE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can delete their own approval tokens" ON "public"."approval_tokens" FOR DELETE USING (("approver_id" = "auth"."uid"()));



CREATE POLICY "Users can insert their own entries" ON "public"."backupcatalogs" FOR INSERT WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can read only their own entries" ON "public"."backupcatalogs" FOR SELECT USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can read profiles" ON "public"."user_profiles" FOR SELECT USING ((("auth"."uid"() = "id") OR (EXISTS ( SELECT 1
   FROM "public"."user_profiles" "user_profiles_1"
  WHERE (("user_profiles_1"."id" = "auth"."uid"()) AND ("user_profiles_1"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can update only their own entries" ON "public"."backupcatalogs" FOR UPDATE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view all groups" ON "public"."internal_groups" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can view group members" ON "public"."group_members" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can view own profile" ON "public"."user_profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view own role" ON "public"."user_profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view steps they are assigned to" ON "public"."recovery_steps_new" FOR SELECT USING (((("assignee_type" = 'user'::"text") AND ("assignee_id" = "auth"."uid"())) OR (("assignee_type" = 'group'::"text") AND (EXISTS ( SELECT 1
   FROM "public"."group_members"
  WHERE (("group_members"."group_id" = "recovery_steps_new"."assignee_id") AND ("group_members"."user_id" = "auth"."uid"()))))) OR (EXISTS ( SELECT 1
   FROM "public"."user_profiles"
  WHERE (("user_profiles"."id" = "auth"."uid"()) AND ("user_profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "Users can view their own approval tokens" ON "public"."approval_tokens" FOR SELECT USING (("approver_id" = "auth"."uid"()));



CREATE POLICY "Users can view their own logs" ON "public"."activity_logs" FOR SELECT USING (("user_id" = "auth"."uid"()));



ALTER TABLE "public"."activity_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."agent_commands" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."agent_response" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."application_groups" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."applications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."approval_tokens" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."audit_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."backup_execution" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."backupcatalogs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."customers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."datacenters" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."datacenters2" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."disks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."group_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."integration_configs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."internal_groups" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."licenses" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."liked_songs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prices" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."products" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_plan_audit_log" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_plan_checkpoints" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_plan_progress" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_plans" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_plans_new" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_steps" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."recovery_steps_new" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."snapshot_schedules" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."songs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscriptions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sync_stats" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tasktable" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."vmconfig" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."vms" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."agent_commands";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."agent_response";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."prices";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."products";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."recovery_plans_new";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."recovery_steps_new";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."tasktable";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."check_step_approval"("step_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."check_step_approval"("step_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_step_approval"("step_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."ensure_user_profiles"() TO "anon";
GRANT ALL ON FUNCTION "public"."ensure_user_profiles"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."ensure_user_profiles"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_user_deletion"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_user_deletion"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_user_deletion"() TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."log_user_action"("action" "text", "entity_id" "uuid", "details" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."log_user_action"("action" "text", "entity_id" "uuid", "details" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_user_action"("action" "text", "entity_id" "uuid", "details" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."log_user_profile_deletion"() TO "anon";
GRANT ALL ON FUNCTION "public"."log_user_profile_deletion"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_user_profile_deletion"() TO "service_role";



GRANT ALL ON FUNCTION "public"."log_user_profile_update"() TO "anon";
GRANT ALL ON FUNCTION "public"."log_user_profile_update"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_user_profile_update"() TO "service_role";



GRANT ALL ON FUNCTION "public"."resolve_step_assignees"("step_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."resolve_step_assignees"("step_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."resolve_step_assignees"("step_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_role"("user_id" "uuid", "new_role" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_role"("user_id" "uuid", "new_role" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_role"("user_id" "uuid", "new_role" "text") TO "service_role";


















GRANT ALL ON TABLE "public"."activity_logs" TO "anon";
GRANT ALL ON TABLE "public"."activity_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."activity_logs" TO "service_role";



GRANT ALL ON TABLE "public"."agent_commands" TO "anon";
GRANT ALL ON TABLE "public"."agent_commands" TO "authenticated";
GRANT ALL ON TABLE "public"."agent_commands" TO "service_role";



GRANT ALL ON SEQUENCE "public"."agent_commands_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."agent_commands_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."agent_commands_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."agent_response" TO "anon";
GRANT ALL ON TABLE "public"."agent_response" TO "authenticated";
GRANT ALL ON TABLE "public"."agent_response" TO "service_role";



GRANT ALL ON SEQUENCE "public"."agent_response_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."agent_response_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."agent_response_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."application_groups" TO "anon";
GRANT ALL ON TABLE "public"."application_groups" TO "authenticated";
GRANT ALL ON TABLE "public"."application_groups" TO "service_role";



GRANT ALL ON SEQUENCE "public"."application_groups_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."application_groups_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."application_groups_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."applications" TO "anon";
GRANT ALL ON TABLE "public"."applications" TO "authenticated";
GRANT ALL ON TABLE "public"."applications" TO "service_role";



GRANT ALL ON SEQUENCE "public"."applications_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."applications_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."applications_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."approval_tokens" TO "anon";
GRANT ALL ON TABLE "public"."approval_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."approval_tokens" TO "service_role";



GRANT ALL ON TABLE "public"."audit_logs" TO "anon";
GRANT ALL ON TABLE "public"."audit_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."audit_logs" TO "service_role";



GRANT ALL ON TABLE "public"."backup_execution" TO "anon";
GRANT ALL ON TABLE "public"."backup_execution" TO "authenticated";
GRANT ALL ON TABLE "public"."backup_execution" TO "service_role";



GRANT ALL ON TABLE "public"."backupcatalogs" TO "anon";
GRANT ALL ON TABLE "public"."backupcatalogs" TO "authenticated";
GRANT ALL ON TABLE "public"."backupcatalogs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."backupcatalogs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."backupcatalogs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."backupcatalogs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."customers" TO "anon";
GRANT ALL ON TABLE "public"."customers" TO "authenticated";
GRANT ALL ON TABLE "public"."customers" TO "service_role";



GRANT ALL ON TABLE "public"."datacenters" TO "anon";
GRANT ALL ON TABLE "public"."datacenters" TO "authenticated";
GRANT ALL ON TABLE "public"."datacenters" TO "service_role";



GRANT ALL ON TABLE "public"."datacenters2" TO "anon";
GRANT ALL ON TABLE "public"."datacenters2" TO "authenticated";
GRANT ALL ON TABLE "public"."datacenters2" TO "service_role";



GRANT ALL ON SEQUENCE "public"."datacenters2_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."datacenters2_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."datacenters2_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."datacenters_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."datacenters_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."datacenters_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."disks" TO "anon";
GRANT ALL ON TABLE "public"."disks" TO "authenticated";
GRANT ALL ON TABLE "public"."disks" TO "service_role";



GRANT ALL ON TABLE "public"."group_members" TO "anon";
GRANT ALL ON TABLE "public"."group_members" TO "authenticated";
GRANT ALL ON TABLE "public"."group_members" TO "service_role";



GRANT ALL ON TABLE "public"."integration_configs" TO "anon";
GRANT ALL ON TABLE "public"."integration_configs" TO "authenticated";
GRANT ALL ON TABLE "public"."integration_configs" TO "service_role";



GRANT ALL ON TABLE "public"."internal_groups" TO "anon";
GRANT ALL ON TABLE "public"."internal_groups" TO "authenticated";
GRANT ALL ON TABLE "public"."internal_groups" TO "service_role";



GRANT ALL ON TABLE "public"."licenses" TO "anon";
GRANT ALL ON TABLE "public"."licenses" TO "authenticated";
GRANT ALL ON TABLE "public"."licenses" TO "service_role";



GRANT ALL ON TABLE "public"."liked_songs" TO "anon";
GRANT ALL ON TABLE "public"."liked_songs" TO "authenticated";
GRANT ALL ON TABLE "public"."liked_songs" TO "service_role";



GRANT ALL ON TABLE "public"."prices" TO "anon";
GRANT ALL ON TABLE "public"."prices" TO "authenticated";
GRANT ALL ON TABLE "public"."prices" TO "service_role";



GRANT ALL ON TABLE "public"."products" TO "anon";
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_plan_audit_log" TO "anon";
GRANT ALL ON TABLE "public"."recovery_plan_audit_log" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_plan_audit_log" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_plan_checkpoints" TO "anon";
GRANT ALL ON TABLE "public"."recovery_plan_checkpoints" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_plan_checkpoints" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_plan_progress" TO "anon";
GRANT ALL ON TABLE "public"."recovery_plan_progress" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_plan_progress" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_plans" TO "anon";
GRANT ALL ON TABLE "public"."recovery_plans" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_plans" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_plans_new" TO "anon";
GRANT ALL ON TABLE "public"."recovery_plans_new" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_plans_new" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_steps" TO "anon";
GRANT ALL ON TABLE "public"."recovery_steps" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_steps" TO "service_role";



GRANT ALL ON TABLE "public"."recovery_steps_new" TO "anon";
GRANT ALL ON TABLE "public"."recovery_steps_new" TO "authenticated";
GRANT ALL ON TABLE "public"."recovery_steps_new" TO "service_role";



GRANT ALL ON TABLE "public"."snapshot_schedules" TO "anon";
GRANT ALL ON TABLE "public"."snapshot_schedules" TO "authenticated";
GRANT ALL ON TABLE "public"."snapshot_schedules" TO "service_role";



GRANT ALL ON TABLE "public"."songs" TO "anon";
GRANT ALL ON TABLE "public"."songs" TO "authenticated";
GRANT ALL ON TABLE "public"."songs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."songs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."songs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."songs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."subscriptions" TO "service_role";



GRANT ALL ON TABLE "public"."sync_stats" TO "anon";
GRANT ALL ON TABLE "public"."sync_stats" TO "authenticated";
GRANT ALL ON TABLE "public"."sync_stats" TO "service_role";



GRANT ALL ON TABLE "public"."tasktable" TO "anon";
GRANT ALL ON TABLE "public"."tasktable" TO "authenticated";
GRANT ALL ON TABLE "public"."tasktable" TO "service_role";



GRANT ALL ON SEQUENCE "public"."tasktable_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."tasktable_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."tasktable_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



GRANT ALL ON TABLE "public"."vmconfig" TO "anon";
GRANT ALL ON TABLE "public"."vmconfig" TO "authenticated";
GRANT ALL ON TABLE "public"."vmconfig" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vmconfig_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vmconfig_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vmconfig_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vms" TO "anon";
GRANT ALL ON TABLE "public"."vms" TO "authenticated";
GRANT ALL ON TABLE "public"."vms" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vms_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vms_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vms_id_seq" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
