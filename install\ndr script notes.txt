# orkestrate/ndr links
# organization
https://hub.docker.com/repositories/jfutey # docker hub repos
https://supabase.com/dashboard/project/utcestwwfefexcnmjcjr # remote supabase db
https://github.com/kamlad/nextdr/tree/master # git repo
https://linear.app/nextdr/my-issues/assigned # deliverable tracking

# supporting
https://supabase.com/docs/guides/self-hosting/docker # local supabase in docker container
https://docs.docker.com/reference/cli/docker/ # docker cli reference
https://docs.docker.com/get-started/workshop/02_our_app/ # containerize anything
https://www.docker.com/blog/getting-started-with-docker-using-node-jspart-i/ # docker and node specifically
https://code.visualstudio.com/docs/typescript/typescript-compiling # compiling typescript
https://docs.docker.com/engine/network/ # network bridging
https://docs.docker.com/engine/install/ # engine install cmds for various OS platforms
https://docs.github.com/en/authentication/connecting-to-github-with-ssh #github ssh authentication
https://github.com/settings/keys # configure your ssh keys


# git clone --branch <branch-name> <repo-url> <target-dir>
first create and CD to the dir where the branch will be checked out
git clone --<NAME_EMAIL>:kamlad/nextdr.git .

# github ssh auth method
# passphrase: 'nextdr!12'
# step 1: create fresh key (must then be entered in github acct). Do this only once, then simply perform auth step 2 below.
# if you have already done this step once, skip ahead to step 2 immediately.
# if you perform this step again, you will need to perform 2 and 3 also.
ssh-keygen -t ed25519 -C "<EMAIL>"  -f "$(pwd)/id_ed25519" -N 'nextdr!12'
eval "$(ssh-agent -s)" # ensures ssh is running.
cat ~/.ssh/id_ed25519.pub # verifies contents.

# step 2: on fresh vm or whereever you have never checked out the code
first, scp id_ed25519 and pub to a local folder on vm
cp id_ed25519 ~/.ssh
cp id_ed25519.pub ~/.ssh
chmod 600 ~/.ssh/id_ed25519

# step 3: authenticate with key
ssh-add ~/.ssh/id_ed25519
ssh -T **************
optional: git config --global credential.helper store
optional: git config --global credential.helper 'cache --timeout=3600'


# building nextdr UI docker image
from ui dir (where dockerfile is)
docker build --tag nextdr-ui:v1.0 .

# to verify image created
docker image ls

# to start container on listening port
docker run -d -p 127.0.0.1:3000:3000 nextdr-ui:v1.0

#to verify container is running
docker ps

# to verify container is responding
curl --request POST \
  --url http://localhost:3000/test \
  --header 'content-type: application/json' \
  --data '{
	"msg": "testing"
}'
{"code":"success","payload":[{"msg":"testing","id":"dc0e2c2b-793d-433c-8645-b3a553ea26de","createDate":"2020-09-01T17:36:09.897Z"}]}

# docker engine cleanup
https://stackoverflow.com/questions/45357771/stop-and-remove-all-docker-containers

#Stop all the containers
docker stop -f $(docker ps -a -q)

#Remove all the containers
docker rm -f $(docker ps -a -q)

#Deleting no longer needed containers (stopped)
docker container prune -f

#Deleting no longer needed images which means, that it only deletes images, which are not tagged and are not pointed on by "latest" - so no real images you can regularly use are deleted
docker image prune -a -f

#Delete all volumes, which are not used by any existing container ( even stopped containers do claim volumes ). This usually cleans up dangling anon-volumes of containers have been deleted long time ago. It should never delete named volumes since the containers of those should exists / be running. Be careful, ensure your stack at least is running before going with this one

docker volume prune -f

#Same for unused networks
docker network prune -f

#And finally, if you want to get rid if all the trash - to ensure nothing happens to your production, be sure all stacks are running and then run
docker system prune -f

# module install TODO's

# all 3 modules
docker bridge network for connectivity across all containers. DONE.

# service
docker bridge network. DONE.
supabase url (populate in .env file?)
supabase key/token
run time environment variables need to be put in the compose file in environment: or env_file: sections.
for sensitive information, we can use docker secrets CLI to pass but this requires a compose file which we dont use for our modules.
We can also use docker run with -e or --env to inject sensitive vars.
generate local supabase container token via cli? is token kept in table? populate with schema? Token can only be created via dashboard, not cli.

DASHBOARD_USERNAME=supabase
DASHBOARD_PASSWORD=NextDR!12
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQ0NjAzMjAwLAogICJleHAiOiAxOTAyMzY5NjAwCn0.xxSBxUg6Fa2MjCrelPwg4zGcpxKwWKvKOQxrgezhRsY
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDQ2MDMyMDAsCiAgImV4cCI6IDE5MDIzNjk2MDAKfQ.HI_Ivqj7JlfiYW9y-WMh5KsdlFG85ilNwJ8Gogr6HYE

oot@TestVM1:/opt/nextdr/master/install# docker logs --details -f 04028f488537 | svc-error.log
 node:internal/modules/cjs/loader:1404
   throw err;
   ^
 
 Error: Cannot find module '/app/dist/server.js'
     at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
     at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
     at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
     at Function._load (node:internal/modules/cjs/loader:1211:37)
     at TracingChannel.traceSync (node:diagnostics_channel:322:14)
     at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
     at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)
     at node:internal/main/run_main_module:36:49 {
   code: 'MODULE_NOT_FOUND',
   requireStack: []
 }
 
 Node.js v22.16.0

can docker container be change to look in a different folder than "dist"?  Error: Cannot find module '/app/dist/server.js'
     at Function._resolveFilename 

# notes
# docker desktop in linux requires KVM support. This is not available in VirtualBox when running a linux VM on windows bare metal (requires linux on linux host, then you cna enable the KVM option in virtual box in the processor tab).
# Docker does not provide support for running Docker Desktop for Linux in nested virtualization scenarios. 
# We recommend that you run Docker Desktop for Linux natively on supported distributions.
# docker core services are fine and will run all required modules for NextDR supabase.
# https://docs.docker.com/desktop/setup/install/linux/

# Main linux distro families. test with one of each for coverage of all distros.
# debian: debian, ubuntu, linux mint, pop!_os
# redhat: redhat, centos, fedora, rocky linux
# suse: suse, opensuse
# less common, not required for testing
# arch: arch linux, manjaro
# alpine: alpine linux
# osx: macos

# VM notes
# after copying initial file
# sudo chown -R vboxuser /path/to/directory
# sudo chmod -R 777 /path/to/directory
# dos2unix /path/to/directory/filename.sh

# INSTALL notes and issues
# if docker engine install continues to have issues, use alternalte convenience script: https://get.docker.com/
# handling updates: For security reasons, we "pin" the versions of each service in the docker-compose file (these versions are updated ~monthly). 
  # If you want to update any services immediately, you can do so by updating the version number in the docker compose file and then running docker compose pull. You can find all the latest docker images in the Supabase Docker Hub.
  # You'll want to update the Studio(Dashboard) frequently to get the latest features and bug fixes. To update the Dashboard:
  # Visit the supabase/studio image in the Supabase Docker Hub
  # Find the latest version (tag) number. It will look something like 20241029-46e1e40
  # Update the image field in the docker-compose.yml file to the new version. It should look like this: image: supabase/studio:20241028-a265374
  # Run docker compose pull and then docker compose up -d to restart the service with the new version.
# do we need an ndrAdmin.sh script to do simple things like up/down containers or can this added to the install script?

# DEVOPS notes and issues
# docker images can be extracted from docker's internal storage system for archival
# docker save -o my-image.tar my-image:latest
# Then you can:
# Inspect the .tar with tar -tvf my-image.tar
# Move it to another system
# Restore it with docker load -i my-image.tar
# this could be useful for offline install or preserving image files in git repo?
# TODO - test automated sequence of create image, save image, remove image, restore image, create container.
