import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Datacenters from "./pages/Datacenters";
import Applications from "./pages/Applications";
import RecoveryPlan from "./pages/RecoveryPlan";
import BackupCatalogs from "./pages/BackupCatalogs";
import License from "./pages/License";
import Login from "./pages/Login";
import ResetPassword from "./pages/reset-password";
import Users from "./pages/Users";
import InternalGroups from "./pages/InternalGroups";
import Integrations from "./pages/admin/Integrations";
import Approve from "./pages/Approve";
import Sidebar from "./components/layout/Sidebar";
import { AuthProvider } from "./lib/context/AuthContext";
import { LicenseProvider } from "./lib/context/LicenseContext";
import RequireAuth from "./components/auth/RequireAuth";
import { RequireAdmin } from "./components/auth/RequireAdmin";

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			staleTime: 60 * 1000,
			retry: 1,
		},
	},
});

function App() {
	return (
		<TooltipProvider>
			<QueryClientProvider client={queryClient}>
				<AuthProvider>
					<LicenseProvider>
						<BrowserRouter>
							<div className="flex h-screen bg-background">
								<Sidebar />
								<main className="flex-1 overflow-y-auto">
									<Routes>
										<Route path="/login" element={<Login />} />
										<Route path="/approve/:token" element={<Approve />} />
										<Route
											path="/"
											element={
												<RequireAuth>
													<Index />
												</RequireAuth>
											}
										/>
										<Route
											path="/datacenters"
											element={
												<RequireAuth>
													<Datacenters />
												</RequireAuth>
											}
										/>
										<Route
											path="/applications"
											element={
												<RequireAuth>
													<Applications />
												</RequireAuth>
											}
										/>
										<Route
											path="/recoveryplan"
											element={
												<RequireAuth>
													<RecoveryPlan />
												</RequireAuth>
											}
										/>
										<Route
											path="/backupcatalogs"
											element={
												<RequireAuth>
													<BackupCatalogs />
												</RequireAuth>
											}
										/>
										<Route
											path="/license"
											element={
												<RequireAuth>
													<License />
												</RequireAuth>
											}
										/>
										<Route
											path="/users"
											element={
												<RequireAuth>
													<RequireAdmin>
														<Users />
													</RequireAdmin>
												</RequireAuth>
											}
										/>
										<Route
											path="/internal-groups"
											element={
												<RequireAuth>
													<RequireAdmin>
														<InternalGroups />
													</RequireAdmin>
												</RequireAuth>
											}
										/>
										<Route
											path="/integrations"
											element={
												<RequireAuth>
													<RequireAdmin>
														<Integrations />
													</RequireAdmin>
												</RequireAuth>
											}
										/>
										<Route path="*" element={<NotFound />} />
									</Routes>
								</main>
							</div>
						</BrowserRouter>
					</LicenseProvider>
				</AuthProvider>
				<Toaster />
				<Sonner />
			</QueryClientProvider>
		</TooltipProvider>
	);
}

export default App;
