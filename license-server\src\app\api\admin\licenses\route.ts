import { NextResponse } from "next/server";
import { createClient, isAdmin } from "@/lib/supabase/server";

export async function GET(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const supabase = await createClient();
		const { data: licenses, error } = await supabase.from("licenses").select(`
				*,
				customer_profiles!inner(company_name)
			`);

		if (error) throw error;

		return NextResponse.json(licenses);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}

export async function POST(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { customerId, features, expiresAt } = await request.json();

		if (!customerId || !features || !expiresAt) {
			return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
		}

		// Import the license generation function
		const { generateLicenseKey } = await import("@/lib/utils/license");

		// Generate the license
		const license = await generateLicenseKey(customerId, features, new Date(expiresAt));

		return NextResponse.json(license);
	} catch (error: any) {
		console.error("License generation error:", error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}

export async function DELETE(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const licenseId = searchParams.get("licenseId");

		if (!licenseId) {
			return NextResponse.json({ error: "License ID is required" }, { status: 400 });
		}

		// Import the license deactivation function
		const { deactivateLicense } = await import("@/lib/utils/license");

		// Deactivate the license
		await deactivateLicense(licenseId);

		return NextResponse.json({ success: true });
	} catch (error: any) {
		console.error("License deactivation error:", error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
