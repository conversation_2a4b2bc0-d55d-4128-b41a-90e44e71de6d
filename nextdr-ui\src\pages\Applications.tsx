import React, { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { Plus, Folder, Archive, RefreshCw, Loader2, Camera, Database } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import PageLayout from "@/components/layout/PageLayout";
import { ApplicationGroup } from "@/lib/types";
import { useModalStore } from "@/lib/store/useStore";
import ApplicationGroupModal from "@/components/modals/ApplicationGroupModal";
import ScheduleModal from "@/components/modals/ScheduleModal";
import SnapshotModal from "@/components/modals/SnapshotModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import AppMenu from "@/components/application/AppMenu";
import AddInstanceDialog from "@/components/application/AddInstanceDialog";
import SnapshotList from "@/components/application/SnapshotList";
import { useToast } from "@/components/ui/use-toast";
import {
	useApplicationGroups,
	useAddVMToGroup,
	useRemoveVMFromGroup,
	useUpdateApplicationGroup,
	useDeleteApplicationGroup,
} from "@/lib/api/hooks/applicationGroups";
import { useRecoveryPlans } from "@/lib/api/hooks/recoveryPlans";
import { useVMsInZone } from "@/lib/api/hooks/vms";
import { useDatacenters } from "@/lib/api/hooks/datacenters";
import { getVMsInZone } from "@/lib/api/api-client";

const ApplicationsPage = () => {
	const [selectedGroup, setSelectedGroup] = useState<ApplicationGroup | null>(null);
	const [isAddInstanceDialogOpen, setIsAddInstanceDialogOpen] = useState(false);
	const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
	const { data: appGroups = [], isLoading: isLoadingAppGroups } = useApplicationGroups();
	const { onOpen } = useModalStore();
	const { toast } = useToast();
	const addVMToGroup = useAddVMToGroup();
	const removeVMFromGroup = useRemoveVMFromGroup();
	const deleteApplicationGroup = useDeleteApplicationGroup();

	const { data: datacenters = [] } = useDatacenters();

	const selectedDatacenter = useMemo(() => {
		if (!selectedGroup || !datacenters.length) return null;
		console.log("selectedDatacenter", selectedGroup.data_center_id);
		return datacenters.find((dc) => dc.project_id === selectedGroup.data_center_id);
	}, [selectedGroup, datacenters]);

	const { data: vmData, isLoading: isLoadingVMData } = useVMsInZone(
		selectedDatacenter?.zone || "us-central1-c",
		selectedDatacenter?.project_id || "",
		selectedDatacenter?.id || "",
		{
			enabled: !!selectedGroup?.data_center_id && !!selectedDatacenter?.project_id,
		}
	);

	const handleSelectGroup = (group: ApplicationGroup) => {
		setSelectedGroup(group);
	};

	const handleAddVM = async (instances: any[]) => {
		if (!selectedGroup) return;

		try {
			toast({
				title: "Adding VMs...",
				description: "Please wait while we add the VMs to the application group",
			});

			const updatedGroup = {
				...selectedGroup,
				vm_ids: [
					...selectedGroup.vm_ids,
					...instances.map((instance) => instance.id || instance.name),
				],
			};
			setSelectedGroup(updatedGroup);

			for (const instance of instances) {
				await addVMToGroup.mutateAsync({
					groupId: selectedGroup.id,
					vmId: instance.id || instance.name,
				});
			}

			setIsAddInstanceDialogOpen(false);

			toast({
				title: "Success",
				description: "VMs added to application group",
			});
		} catch (error) {
			console.error("Error adding VM to group:", error);
			toast({
				title: "Error",
				description: "Failed to add VM to application group",
				variant: "destructive",
			});
		}
	};

	const handleRemoveVM = async (groupId: string, vmId: string) => {
		try {
			await removeVMFromGroup.mutateAsync({
				groupId,
				vmId,
			});
			toast({
				title: "Success",
				description: "VM removed from application group",
			});
		} catch (error) {
			console.error("Error removing VM from group:", error);
			toast({
				title: "Error",
				description: "Failed to remove VM from application group",
				variant: "destructive",
			});
		}
	};

	const handleEditGroup = () => {
		if (selectedGroup) {
			onOpen("applicationGroup", { group: selectedGroup });
		}
	};

	const handleDeleteGroup = () => {
		if (!selectedGroup) return;
		setIsConfirmDeleteOpen(true);
	};

	const confirmDeleteGroup = async () => {
		if (!selectedGroup) return;

		try {
			await deleteApplicationGroup.mutateAsync(selectedGroup.id);
			setSelectedGroup(null);
			toast({
				title: "Success",
				description: "Application group deleted successfully",
			});
		} catch (error) {
			console.error("Error deleting application group:", error);
			toast({
				title: "Error",
				description: "Failed to delete application group",
				variant: "destructive",
			});
		}
	};

	const container = {
		hidden: { opacity: 0 },
		show: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1,
			},
		},
	};

	const item = {
		hidden: { opacity: 0, y: 20 },
		show: { opacity: 1, y: 0, transition: { duration: 0.4 } },
	};

	const fetchInstancesAdapter = async (projectId: string, datacenterId: string) => {
		const zone = selectedDatacenter?.zone || "us-central1-c";
		console.log("fetchInstancesAdapter", zone, projectId, datacenterId);
		const vms = await getVMsInZone(zone, projectId, datacenterId);

		return vms.map((vm) => ({
			id: vm.name,
			name: vm.name,
			machineType: vm.machineType,
			zone: vm.zone,
			status: vm.status,
		}));
	};

	return (
		<PageLayout title="Applications">
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-2xl font-bold">Application Groups</h1>
					<p className="text-muted-foreground">Manage your application groups and protection</p>
				</div>
				<Button
					className="bg-dr-purple hover:bg-dr-purple-dark"
					onClick={() => onOpen("applicationGroup")}
				>
					<Plus className="mr-2 h-4 w-4" />
					New Application Group
				</Button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-4 gap-5">
				<motion.div
					variants={container}
					initial="hidden"
					animate="show"
					className="lg:col-span-1 space-y-4"
				>
					{isLoadingAppGroups ? (
						<div className="flex justify-center items-center py-8">
							<Loader2 className="h-6 w-6 animate-spin mr-2" />
							<span>Loading application groups...</span>
						</div>
					) : appGroups.length > 0 ? (
						appGroups.map((group) => (
							<motion.div
								key={group.id}
								variants={item}
								onClick={() => handleSelectGroup(group)}
								className={`cursor-pointer transition-colors ${
									selectedGroup?.id === group.id ? "border-dr-purple" : "border-border"
								}`}
							>
								<Card
									className={`overflow-hidden ${
										selectedGroup?.id === group.id ? "border-dr-purple bg-secondary/50" : "bg-card"
									}`}
								>
									<CardContent className="p-4">
										<div className="flex items-center">
											<div className="flex-shrink-0 mr-4 p-2 rounded-full bg-blue-900/20 text-blue-500">
												<Folder className="h-5 w-5" />
											</div>
											<div className="flex-1">
												<h3 className="font-medium">{group.name}</h3>
												<div className="text-xs text-muted-foreground">
													{group.vm_ids?.length || 0} VMs • Created{" "}
													{new Date(group.created_at).toLocaleDateString()}
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						))
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<p>No application groups found</p>
							<Button
								onClick={() => onOpen("applicationGroup")}
								variant="outline"
								size="sm"
								className="mt-2"
							>
								Create Application Group
							</Button>
						</div>
					)}
				</motion.div>

				<div className="lg:col-span-3">
					{selectedGroup ? (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
						>
							<Card>
								<CardContent className="p-6">
									<div className="flex justify-between items-center mb-4">
										<div className="flex items-center">
											<div className="p-2 rounded-full bg-blue-900/20 mr-4">
												<Folder className="h-5 w-5 text-blue-500" />
											</div>
											<div>
												<h2 className="text-xl font-bold">{selectedGroup.name}</h2>
												<p className="text-sm text-muted-foreground">{selectedGroup.description}</p>
											</div>
										</div>
										<div className="flex items-center space-x-2">
											<Button
												variant="outline"
												size="sm"
												onClick={() => onOpen("backupSchedule")}
												className="flex items-center"
											>
												<Database className="h-4 w-4 mr-2" />
												Backup Schedule
											</Button>
											{/* <Button
												variant="outline"
												size="sm"
												onClick={() => onOpen("snapshotSchedule")}
												className="flex items-center"
											>
												<Camera className="h-4 w-4 mr-2" />
												Snapshot Schedule
											</Button> */}
											<Button variant="outline" size="sm" onClick={handleEditGroup}>
												Edit
											</Button>
											<Button variant="destructive" size="sm" onClick={handleDeleteGroup}>
												Delete
											</Button>
										</div>
									</div>

									{/* Application Group Resources */}
									<div className="mt-6">
										<AppMenu
											selectedGroup={selectedGroup}
											handleRemoveVM={handleRemoveVM}
											onAddVM={() => setIsAddInstanceDialogOpen(true)}
											isLoadingInstances={isLoadingVMData}
											datacenterId={selectedDatacenter?.id || ""}
											id={
												datacenters.find((dc) => dc.project_id === selectedGroup.data_center_id)
													?.id || ""
											}
										/>
									</div>

									{/* Snapshots List - Independent of tabs */}
									<SnapshotList selectedGroup={selectedGroup} />
								</CardContent>
							</Card>
						</motion.div>
					) : (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
							className="h-full flex items-center justify-center"
						>
							<div className="text-center">
								<Folder className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
								<h3 className="text-lg font-medium mb-1">No application group selected</h3>
								<p className="text-muted-foreground mb-4">
									Select an application group or create a new one
								</p>
								<Button
									className="bg-dr-purple hover:bg-dr-purple-dark"
									onClick={() => onOpen("applicationGroup")}
								>
									<Plus className="mr-2 h-4 w-4" />
									New Application Group
								</Button>
							</div>
						</motion.div>
					)}
				</div>
			</div>

			<ApplicationGroupModal />

			{/* Delete Confirmation Modal */}
			<DeleteConfirmationModal
				isOpen={isConfirmDeleteOpen}
				onClose={() => setIsConfirmDeleteOpen(false)}
				onConfirm={confirmDeleteGroup}
				title="Delete Application Group"
				description={`Are you sure you want to delete the application group "${selectedGroup?.name}"? This action cannot be undone.`}
			/>

			{selectedGroup && (
				<>
					<AddInstanceDialog
						isOpen={isAddInstanceDialogOpen}
						onClose={() => setIsAddInstanceDialogOpen(false)}
						onInstancesSelect={handleAddVM}
						selectedGroup={selectedGroup}
						fetchInstances={fetchInstancesAdapter}
						datacenterId={selectedDatacenter?.id || ""}
					/>

					{/* Backup Schedule Modal */}
					<ScheduleModal
						type="backup"
						selectedGroup={selectedGroup}
						projectId={selectedDatacenter?.project_id || ""}
						zone={selectedDatacenter?.zone || "us-central1-c"}
						datacenterId={selectedDatacenter?.id || ""}
					/>

					{/* Reusable Snapshot Modal (handles both create and edit) */}
					<SnapshotModal
						selectedGroup={selectedGroup}
						projectId={selectedDatacenter?.project_id || ""}
						zone={selectedDatacenter?.zone || "us-central1-c"}
						datacenterId={selectedDatacenter?.id || ""}
					/>
				</>
			)}
		</PageLayout>
	);
};

export default ApplicationsPage;
