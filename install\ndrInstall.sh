#!/bin/bash

gNDR_INSTALL_MODE=true

source "$(dirname "${BASH_SOURCE[0]}")/ndrRegistry.sh"
source "$(dirname "${BASH_SOURCE[0]}")/ndrDocker.sh"
source "$(dirname "${BASH_SOURCE[0]}")/ndrSupabase.sh"

# --- CONFIGURATION ---

gLOG_FILE="/var/log/ndrInstall.log"


EXPRESS_MENU_OPTIONS="installprerequisites | installsupabase | installservice | installui | installall | removesupabase | removeservice | removeui | removeall"

# ----------------------

# --- FUNCTIONS ---



function installSupabaseApplication ()
{
  # Create your desired working directory:
  # Tree should look like this
  # .
  # ├── supabase
  # └── supabase-project
  local logSectionDesc="Cleaning and creating new application folders"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  # check up front if schema var is empy or file does not exist before doing any irreversable work
  if [ -z "$gACTIVE_NDR_SQL_SCHEMA_FILE" ] || [ ! -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    ndr_logError "No active $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] found."
    return 1
  fi
  
  rm --recursive --force "$gSUPABASE_NEXTDR_HOME"
  rm --recursive --force "$gSUPABASE_TEMPLATE_HOME"
  mkdir "$gSUPABASE_NEXTDR_HOME"
  mkdir "$gSUPABASE_TEMPLATE_HOME"
  ndr_logSecEnd "$logSectionDesc"

  # To pull supabase docker code from GIT
  # This is a very lengthy process and will take several minutes or more depending on connectivity speed.
  local logSectionDesc="Cloning a local supabase container from GIT"
  ndr_logSecStart "$logSectionDesc"

  cd "$gSUPABASE_TEMPLATE_HOME" || { ndr_logError "Failed to cd into supabase template dir [$gSUPABASE_TEMPLATE_HOME]"; return 1; }

  # basic clone command
  #git clone --depth 1 https://github.com/supabase/supabase
  # advanced clone command
  git clone --filter=blob:none --no-checkout https://github.com/supabase/supabase "$gSUPABASE_TEMPLATE_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Git clone failed."
    return 1
  fi
  ndr_logInfo "Git clone for Supabase completed."

  git sparse-checkout set --cone docker && git checkout master
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "GIT sparse checkout failed."
    return 1
  fi
  ndr_logInfo "Git sparse checkout for Supabase completed."

  cd "$gSCRIPT_HOME_DIR" || { ndr_logError "Failed to cd into install dir"; return 1; }
  ndr_logSecEnd "$logSectionDesc"

  # Copy the compose files over to your project
  local logSectionDesc="Copying the compose files to project"
  ndr_logSecStart "$logSectionDesc"
  composeSrcLocation="$gSUPABASE_TEMPLATE_HOME/docker/*"
  composeDestLocation="$gSUPABASE_NEXTDR_HOME"
  cp -rf $composeSrcLocation $composeDestLocation # note: DO NOT quote the args for cp command, otherwise it will not copy the files correctly with a stat failure.
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy Base docker compose files [$composeSrcLocation] to [$composeDestLocation]"
    return 1
  fi
  ndr_logInfo "Base docker compose files [$composeSrcLocation] copied to [$composeDestLocation]"
  
  if [[ $gSUPABASE_COMPOSE_COPY_MODE -eq 1 ]]; then
    # Copy the NextDR specific docker compose file to the project. This custom sql script will be used to create the NDR tables and RLS policies.
    # This mode is not currently the default behavior since we now prefer to edit the base docker package compose file directly.
    # Using the base docker compose file for the NDR container is a better practice since the file could evolve over time and this would ensure we always have the latest version.
    cp "$gSCRIPT_HOME_DIR/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE" "$composeDestLocation/docker-compose.yml"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Docker compose file [$PWD/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$composeDestLocation]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Docker compose file [$PWD/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$composeDestLocation]"
  else
    ndr_customizeSupabaseDockerComposeFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to customize $gCOMPANY_NAME Docker compose file."
      return 1
    fi
    ndr_logInfo "Successfully customized $gCOMPANY_NAME Docker compose file."
  fi

  ndr_ParseComposeFileServices "$composeDestLocation/docker-compose.yml"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to parse Docker compose file [$composeDestLocation/docker-compose.yml] service entries."
    return 1
  fi
  ndr_logInfo "Successfully parsed Docker compose file [$composeDestLocation/docker-compose.yml] service entries."

  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  ndr_logInfo "Successfully created registry."

  ndr_RegistryAddKeyServiceEntries
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to add Docker service entries to registry"
    return 1
  fi
  ndr_logInfo "Successfully added Docker compose file service entries to registry."

  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Copying the $gCOMPANY_NAME schema file to project"
  ndr_logSecStart "$logSectionDesc"
  
  # Copy the NDR schema file to the project.
  # This custom sql script will be absorbed by the postgres db docker container at initial startup and will create the NDR tables and RLS policies.
  schemaDestLocation="${gSUPABASE_NEXTDR_HOME}/db-init-scripts"
  mkdir "$schemaDestLocation"
  cp -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" "$schemaDestLocation"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"
    return 1
  fi
  ndr_logInfo "Successfully copied $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"

  ndr_logSecEnd "$logSectionDesc"

  # Copy the custom env vars
  local logSectionDesc="Copying the $gCOMPANY_NAME env file to project"
  ndr_logSecStart "$logSectionDesc"
  envSrcFile="${PWD}/${gNDR_ENV_MASTER_FILENAME}"
  envDestFile="${gSUPABASE_NEXTDR_HOME}/${gNDR_ENV_MODULE_FILENAME}"
  cp -f "$envSrcFile" "$envDestFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Docker ENV file [$envSrcFile] to [$envDestFile]"
    return 1
  fi
  ndr_logInfo "$gCOMPANY_NAME Docker ENV file [$envSrcFile] copied to [$envDestFile]"
  ndr_logSecEnd "$logSectionDesc"

  # Go to the docker folder
  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }
  
  # Pull the latest images
  local logSectionDesc="Pulling latest images for container"
  ndr_logSecStart "$logSectionDesc"
  docker compose pull
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose pull failed"
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  dockerContainerName="ndr-supabase"

  # Start the services (in detached mode)
  local logSectionDesc="Starting container images services"
  ndr_logSecStart "$logSectionDesc"
  
  # create the bridge network if it does not exist
  ndr_createDockerBridgeNetwork
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to create Docker bridge network."
    return 1
  fi
  ndr_logInfo "Docker bridge network created."

  docker compose -p "$dockerContainerName" up -d
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose up failed"
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"

  # check if the newly built Docker containers exist
  ndr_verifySupabaseContainersExist
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker container verification failed."
    return 1
  fi

  # clean up supabase folders
  rm -rf "$gSUPABASE_TEMPLATE_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove Supabase template folder [$gSUPABASE_TEMPLATE_HOME]"
    #return 1
  fi


  # Accessing Supabase Studio
  # You can access Supabase Studio through the API gateway on port 8000. For example: http://<your-ip>:8000, or localhost:8000 if you are running Docker locally.

  # You will be prompted for a username and password. By default, the credentials are:

  # Username: supabase
  # Password: this_password_is_insecure_and_should_be_updated

  # Need to change credentials and secrets to secure values
  # Update the ./docker/.env file with your own secrets. In particular, these are required:

  # POSTGRES_PASSWORD: the password for the postgres role.
  # JWT_SECRET: used by PostgREST and GoTrue, among others.

  # Dashboard authentication
  # The Dashboard is protected with basic authentication. The default user and password MUST be updated before using Supabase in production.
  # Update the following values in the ./docker/.env file:

  # DASHBOARD_PASSWORD: The default password for the Dashboard

  ndr_logInfo "Local supabase container installation is complete. You can access the Supabase Studio through the API gateway on port 8000."
  ndr_logInfo "http://<your-ip>:8000, or http://localhost:8000 if you are running # Docker locally"
  ndr_logInfo "Please check the .env file in the $gSUPABASE_NEXTDR_HOME for dashboard credentials."

  ndr_logSecEnd "$logSectionDesc"
  
  return 0
}



# ====================================================
# Main script execution
# ====================================================



function mainInstallSupabaseApplication ()
{
  local logSectionDesc="Installing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local appBuildOptions="$NDR_SUPABASE_APP_BUILD_OPTIONS_INSTALL"

  ndr_BuildSupabaseApplication "$appBuildOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  ndr_logInfo "Supabase application install completed."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallServiceApplication ()
{
  local logSectionDesc="Installing service application"
  ndr_logSecStart "$logSectionDesc"

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  local dbInstalled=$?
  if [[ "$dbInstalled" -ne 0 ]]; then
    read -p "The prerequisite $gCOMPANY_NAME Supabase database module does not appear to be currently installed. This module has an operational dependency on that module and may not be fully operable until that prerequisite is also installed. Would you like continue installing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with installation."
    fi
  fi

  local dockerImageBaseName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_SERVICE_HOME_LOC"
  local dockerContainerName="$NDR_SERVICE_CONTAINER_NAME"
  local dockerContainerPort="$NDR_SERVICE_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # create container folder in install location.
  local containerHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  mkdir -p "$containerHomeDir"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to create application directory [$containerHomeDir]. Please check permissions and try again."
    return 1
  fi
  ndr_logInfo "Created application directory [$containerHomeDir]."

  # copy the app specific docker file and compose file to app home dir.
  local sourceFile="${gSCRIPT_HOME_DIR}/${NDR_SERVICE_COMPOSEFILE_FILENAME}"
  local destFile="${containerHomeDir}"
  cp -f $sourceFile $destFile
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy [$sourceFile] to [$destFile]"
    return 1
  fi
  ndr_logInfo "Successfully copied [$sourceFile] to [$destFile]"

  sourceFile="${gSCRIPT_HOME_DIR}/${NDR_SERVICE_DOCKERFILE_FILENAME}"
  destFile="${containerHomeDir}/Dockerfile"
  cp -f $sourceFile $destFile
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy [$sourceFile] to [$destFile]"
    return 1
  fi
  ndr_logInfo "Successfully copied [$sourceFile] to [$destFile]"

  cd "$containerHomeDir" || { ndr_logError "Failed to cd into application dir [$containerHomeDir]"; return 1; }

  # pull image from repo
  ndr_DownloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer "$containerHomeDir" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."

  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to create application registry."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallUIApplication ()
{
  local logSectionDesc="Installing UI application"
  ndr_logSecStart "$logSectionDesc"

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  local svcInstalled=$?
  if [[ "$svcInstalled" -ne 0 ]]; then
    read -p "The prerequisite $gCOMPANY_NAME service module does not appear to be currently installed. This module has an operational dependency on that module and may not be fully operable until that prerequisite is also installed. Would you like continue installing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with installation."
    fi
  fi

  local dockerImageBaseName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_UI_HOME_LOC"
  local dockerContainerName="$NDR_UI_CONTAINER_NAME"
  local dockerContainerPort="$NDR_UI_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # create container folder in install location.
  local containerHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  mkdir -p "$containerHomeDir"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to create application directory [$containerHomeDir]. Please check permissions and try again."
    return 1
  fi
  ndr_logInfo "Created application directory [$containerHomeDir]."

  # copy the app specific docker file and compose file to app home dir.
  local sourceFile="${gSCRIPT_HOME_DIR}/${NDR_UI_COMPOSEFILE_FILENAME}"
  local destFile="${containerHomeDir}"
  cp -f $sourceFile $destFile
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy [$sourceFile] to [$destFile]"
    return 1
  fi
  ndr_logInfo "Successfully copied [$sourceFile] to [$destFile]"

  sourceFile="${gSCRIPT_HOME_DIR}/${NDR_UI_DOCKERFILE_FILENAME}"
  destFile="${containerHomeDir}/Dockerfile"
  cp -f $sourceFile $destFile
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy [$sourceFile] to [$destFile]"
    return 1
  fi
  ndr_logInfo "Successfully copied [$sourceFile] to [$destFile]"

  cd "$containerHomeDir" || { ndr_logError "Failed to cd into application dir [$containerHomeDir]"; return 1; }

  # pull image from repo
  ndr_DownloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer "$containerHomeDir" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."
  
  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallAllApplications ()
{
  local logSectionDesc="Installing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainInstallSupabaseApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainInstallServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainInstallUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveSupabaseApplication ()
{
  local logSectionDesc="Removing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  local svcInstalled=$?
  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  local uiInstalled=$?
  if [[ "$svcInstalled" -eq 0 || "$uiInstalled" -eq 0 ]]; then
    read -p "Some dependent $gCOMPANY_NAME product modules appear to be currently installed. Removing this module may adversely affect the operation of these dependencies. Would you like continue removing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with removal."
    fi
  fi

  ndr_SupabaseContainerCleanup
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryRemoveServiceEntries
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to remove Supabase registry services."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


function mainRemoveServiceApplication ()
{
  local logSectionDesc="Removing Service Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_mainCleanupServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  # remove app home dir
  local containerFolder="$NDR_SERVICE_HOME_LOC"
  local containerHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  rm -rf "$containerHomeDir"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove application home dir [$containerHomeDir]"
    #return 1
  else
    ndr_logInfo "Removed application home dir [$containerHomeDir]"
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveUIApplication ()
{
  local logSectionDesc="Removing UI Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_mainCleanupUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  # remove app home dir
  local containerFolder="$NDR_UI_HOME_LOC"
  local containerHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  rm -rf "$containerHomeDir"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove application home dir [$containerHomeDir]"
    #return 1
  else
    ndr_logInfo "Removed application home dir [$containerHomeDir]"
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveAllApplications ()
{
  local logSectionDesc="Removing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainRemoveSupabaseApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainRemoveServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainRemoveUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# command line parsing
# ====================================================
function mainParseCommandLineArgs ()
{
  local logSectionDesc="Parsing command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Default values
  
  ndr_parseCommandLineArgs "$@"

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --password|-p)
        if [[ -n "$2" && "$2" != --* ]]; then
          gSUPABASE_REMOTE_PASSWORD="$2"
          #ndr_logInfo "Supabase set to [$gSUPABASE_REMOTE_PASSWORD]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --remote-token|-rt)
        if [[ -n "$2" && "$2" != --* ]]; then
          supabaseRemotetoken="$2"
          ndr_logInfo "Supabase $1 set to [$gSUPABASE_REMOTE_TOKEN]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --dest|-d)
        if [[ -n "$2" && "$2" != --* ]]; then
          gNEXTDR_HOME_DIR="$2"
          ndr_logInfo "Home directory set to [$gNEXTDR_HOME_DIR]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --help|-h)
        ndr_logWarn "Usage: $0 --express <$EXPRESS_MENU_OPTIONS> --password <password> [--debug]."
        return 1
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Express install mode
# ====================================================
function mainExpressInstallMode ()
{
  local logSectionDesc="Executing express install mode"
  ndr_logSecStart "$logSectionDesc"

  # Check if express mode is enabled and an option is provided
  if [ "$gExpressMode" != 1 ]; then
    ndr_logError "Express mode is not enabled. Use --express to enable."
    return 1
  fi
  if [ -z "$gExpressOption" ]; then
    ndr_logError "No express option provided. Use --express <option>."
    return 1
  fi

  case "$gExpressOption" in
    installprerequisites)
      ndr_checkAndInstallPrerequisites
      ;;
    installsupabase)
      mainInstallSupabaseApplication
      ;;
    installservice)
      mainInstallServiceApplication
      ;;
    installui)
      mainInstallUIApplication
      ;;
    installall)
      mainInstallAllApplications
      ;;
    removesupabaseapp|removesupabase)
      mainRemoveSupabaseApplication
      ;;
    removeserviceapp|removeservice)
      mainRemoveServiceApplication
      ;;
    removeuiapp|removeui)
      mainRemoveUIApplication
      ;;
    removeall)
      mainRemoveAllApplications
      ;;
    *)
      # print usage and exit
      ndr_logWarn "Invalid option. Please use one of the following options: installprerequisites, pullschema, installcontainer, pullandinstall or cleancontainer."
      return 1
      ;;
  esac
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Interactive menu mode
# ====================================================
function mainInteractiveMenuMode ()
{
  local logSectionDesc="Executing interactive menu mode"
  ndr_logSecStart "$logSectionDesc"
  
  MENU_ITEM_1="Check and install software package prerequisites"
  MENU_ITEM_2="Install the $gCOMPANY_NAME Supabase database in a local Docker container"
  MENU_ITEM_3="Install the $gCOMPANY_NAME Service application in a local Docker container"
  MENU_ITEM_4="Install the $gCOMPANY_NAME UI application in a local Docker container"
  MENU_ITEM_5="Install all modules"
  MENU_ITEM_6="Remove existing $gCOMPANY_NAME Supabase Docker application"
  MENU_ITEM_7="Remove existing $gCOMPANY_NAME Service Docker application"
  MENU_ITEM_8="Remove existing $gCOMPANY_NAME UI Docker application"
  MENU_ITEM_9="Remove all modules"
  
  while true; do
    local appVersion=$(version=$(ndr_getVersionReg 2>/dev/null) && echo "$version" || echo "(not installed)")
    local supabaseAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)
    local serviceAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)
    local uiAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)

    echo "===================================================================="
    echo "$gCOMPANY_NAME Software Installation and Package Management Menu"
    echo "Current module installation status:"
    echo "  Supabase: $supabaseAppInstalled"
    echo "  Service: $serviceAppInstalled"
    echo "  UI: $uiAppInstalled"
    echo "  Version: $appVersion"
    echo "===================================================================="
    echo "Please select an option:"
    echo ""
    echo "1. $MENU_ITEM_1"
    echo "2. $MENU_ITEM_2"
    echo "3. $MENU_ITEM_3"
    echo "4. $MENU_ITEM_4"
    echo "5. $MENU_ITEM_5"
    echo "6. $MENU_ITEM_6"
    echo "7. $MENU_ITEM_7"
    echo "8. $MENU_ITEM_8"
    echo "9. $MENU_ITEM_9"
    echo "q. Exit"
    echo ""
    read -p "Enter your choice [1-n]: " choice

    case $choice in
      1)
        echo "$MENU_ITEM_1. This will check for NPM/NPX, GIT, Docker, etc and prompt for interactive install."
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        # default YES logic
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_checkAndInstallPrerequisites
        fi
        ;;
      2)
        echo "$MENU_ITEM_2. Warning, this will overwrite any existing instance and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallSupabaseApplication
        fi
        ;;
      3)
        echo "$MENU_ITEM_3. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallServiceApplication
        fi
        ;;
      4)
        echo "$MENU_ITEM_4. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallUIApplication
        fi
        ;;
      5)
        echo "$MENU_ITEM_5. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallAllApplications
        fi
        ;;
      6)
        echo "$MENU_ITEM_6. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveSupabaseApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      7)
        echo "$MENU_ITEM_7. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveServiceApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      8)
        echo "$MENU_ITEM_8. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveUIApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      9)
        echo "$MENU_ITEM_9. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveAllApplications
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      Q|q)
        echo "Exiting..."
        break
        ;;
      t)
        # unit test - remove for production
        break
        ;;
      *)
        echo "Invalid option. Please try again."
        ;;
    esac

    echo ""
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function main ()
{
  echo "🕓 Started at $(date)"

  mainParseCommandLineArgs "$@"
  return_code=$?
  if [ $return_code != 0 ]; then
    return 1
  fi

  ndr_osTypeCheck

  if [[ "$gExpressMode" -eq 1  && -n "$gExpressOption" ]]; then
    ndr_logInfo "Express install option selected. Skipping all prompts and running with supplied values."
    mainExpressInstallMode
    echo "🕓 Finished at $(date)"
    return 0
  fi

  mainInteractiveMenuMode

  echo "🕓 Finished at $(date)"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
  exit 0
fi
