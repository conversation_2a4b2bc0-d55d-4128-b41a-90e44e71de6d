#!/bin/bash
# ndrDocker.sh - A Bash module providing common Docker related functions

source "$(dirname "${BASH_SOURCE[0]}")/ndrCommon.sh"

# --- FUNCTIONS ---

function ndr_removeDockerPackages ()
{
  local logSectionDesc="Cleaning up packages"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  # Loop through each package and remove it
  for pkg in "${dockerInstallPackages[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  sudo apt-get autoremove --assume-yes
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to autoremove unused packages."
    #return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# returns 2 for usage or general error
# returns 1 for image not found
# returns 0 for image found
# usage: function <image:tag>
function ndr_verifyDockerImageExists () 
{
  local logSectionDesc="Verifying Docker Image Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  local image_name="$1"
  if [[ -z "$image_name" ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  # Run command and capture both output and return status
  while true; do
    
    local output
    #output=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -Fx "$image_name")
    output=$(docker images --format "{{.Repository}}:{{.Tag}}")
    return_code=$?

    # Check if docker failed
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to query for Docker images."
      retVal=2
      break
    fi

    # check if output is empty (no images at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker images found."
      retVal=1
      break
    fi

    #if [[ -n "$output" ]]; then
    #  ndr_logInfo "✅ Docker image '${image_name}' exists."
    #  retVal=0
    #else
    #  ndr_logWarn "No matching image output returned for '${image_name}'."
    #  retVal=1
    #fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$image_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker image '${image_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching image output returned for '${image_name}'."
      retVal=1
    fi
    
    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# returns 2 for usage or general error
# returns 1 for container not found
# returns 0 for container found
# usage: function <container_name>
function ndr_verifyDockerContainerExists () 
{
  local logSectionDesc="Verifying Docker Container Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  local container_name="$1"
  if [[ -z "$container_name" ]]; then
    echo "❌ Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  # Capture the output and return code separately
  while true; do
    
    local output
    #output=$(docker ps -a --format '{{.Names}}' | grep -Fx "$container_name")
    output=$(docker ps -a --format '{{.Names}}')
    local status=$?

    # Check if docker failed
    if [[ $status -ne 0 ]]; then
      ndr_logError "Failed to query for Docker containers."
      retVal=2
      break
    fi

    # check if output is empty (no containers at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker containers found."
      retVal=1
      break
    fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$container_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker container '${container_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching container output returned for '${container_name}'."
      retVal=1
    fi

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# usage: function <image_name> [build_mode_options]
function ndr_cleanupDockerImage() 
{
  local logSectionDesc="Cleaning up Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do

    local image_name="$1"
    if [[ -z "$image_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerImage <image_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker image '${image_name}' not found, no cleanup needed."
      retVal=0
      break
    fi
    
    # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker image '${image_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker image removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🧹 Forcing removal of image '${image_name}'..."
    docker image rm -f "$image_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove image '${image_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker image '${image_name}' removal command succeeded."

    docker buildx prune -f
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logWarn "Failed to prune docker build cache entries."
    fi
    ndr_logInfo "Successfully pruned docker build cache entries."

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      # image still exists after removal attempt
      ndr_logError "Error, image still present after removal '${image_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Image '${image_name}' successfully removed and verified."

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function  <image_name> [build_mode_options]
function ndr_cleanupDockerContainer ()
{
  local logSectionDesc="Cleaning up Docker Container"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do
    local container_name="$1"
    
    if [[ -z "$container_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerContainer <container_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker container '${container_name}' not found, nothing to remove."
      retVal=0
      break
    fi

     # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker container '${container_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker container removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🛑 Stopping container '${container_name}'..."

    docker stop "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logWarn "Failed to stop Docker container '${container_name}'."
      #retVal=1
      #break
    fi

    ndr_logInfo "🧹 Removing container '${container_name}'..."
    
    docker container rm -f "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove Docker container '${container_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker container removal command succeeded."

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      ndr_logError "Error, container still present after removal '${container_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Container '${container_name}' successfully removed and verified."

    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> [build_mode_options]
function ndr_cleanupDockerApplication ()
{
  local logSectionDesc="Cleanup Docker Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName="$4"
  
  local dockerAppManageOptions="${5:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: ndr_cleanupDockerApplication <folder> <image_name> <image_version> <container_name>"
    return 1
  fi

  #local containerPath="$gSCRIPT_HOME_DIR/../$containerFolder"
  if [[ ! -d "$containerFolder" ]]; then
    ndr_logWarn "Container path [$containerFolder] does not exist."
    return 0
  fi

  # move from the install directory to the container/module directory
  cd "$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }

  # check if the Dockerfile exists
  local dockerFile="${containerFolder}/Dockerfile"
  if [[ ! -f "$dockerFile" ]]; then
    ndr_logError "$dockerFile not found in $containerFolder directory."
    return 1
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )); then
    # remove any existing Docker container with the same name
    ndr_cleanupDockerContainer "$dockerContainerName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker container."
      return 1
    fi
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE )); then
    # remove any existing image with the same name
    ndr_cleanupDockerImage "$dockerImageName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker image."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> <container_port> [build_mode_options]
function ndr_buildDockerApplicationContainer ()
{
  local logSectionDesc="Building Docker Application Container"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName=$4
  local dockerContainerPort=$5
  local dockerContainerURL="http://localhost:$dockerContainerPort"

  local dockerAppManageOptions="${6:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 5 ]]; then
    ndr_logError "Usage: buildDockerApplication <folder> <image_name> <image_version> <container_name> <container_port>"
    return 1
  fi

  # move from the install directory to the container/module directory
  #local containerPath="$gSCRIPT_HOME_DIR/../$containerFolder"
  cd "$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }
  
  # check if the Dockerfile exists
  local dockerFile="${containerFolder}/Dockerfile"
  if [[ ! -f "$dockerFile" ]]; then
    ndr_logError "$dockerFile not found in $containerFolder directory."
    return 1
  fi

  dockerAppManageOptions=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT ))
  # remove any existing Docker container with the same name
  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application container."
    return 1
  fi

  # create the bridge network if it does not exist
  ndr_createDockerBridgeNetwork
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to create Docker bridge network."
    return 1
  fi
  
  # run the Docker container
  docker run -d --name "$dockerContainerName" --network "$NDR_DOCKER_BRIDGE_NETWORK_NAME" -p "$dockerContainerPort:$dockerContainerPort" "$dockerImageName"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to run Docker container [$dockerContainerName]."
    return 1
  fi

  # check if the newly built Docker container exists
  ndr_verifyDockerContainerExists "$dockerContainerName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker container verification failed."
    return 1
  fi
  # check if the container is running
  docker ps -a | grep "$dockerContainerName" | grep "Up"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is not running."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running."

  # check if the container is running on specified container port
  docker ps -a | grep "$dockerContainerName" | grep "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is NOT running on port $dockerContainerPort."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running on port $dockerContainerPort."
  
  # execute curl command to check if the container is running
  curl -s -o /dev/null -w "%{http_code}" "$dockerContainerURL"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to access the container at $dockerContainerURL"
    return 1
  fi
  ndr_logInfo "Successfully accessed the container at $dockerContainerURL"
  
  ndr_logInfo "Docker image [$dockerImageName] is built and ready to use."
  ndr_logInfo "Docker container [$dockerContainerName] is running and accessible."
  
  ndr_logInfo "You can access the container at $dockerContainerURL"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_scrubAllDockerResources ()
{
  local logSectionDesc="Force Removing All Local Docker Resources"
  ndr_logSecStart "$logSectionDesc"  

  ndr_logInfo "ALL existing local Docker resources will be removed, both active and inactive. Warning, this action cannot be undone!"
  read -p "Are you sure you want to proceed? [y|N] " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    ndr_logInfo "Proceeding with all local Docker resource removal."
  else
    ndr_logInfo "Operation cancelled, returning to main menu."
    return 0
  fi

  # docker cleanup
  # https://stackoverflow.com/questions/45357771/stop-and-remove-all-docker-containers

  #Stop all the containers
  docker stop $(docker ps -a -q)

  #Remove all the containers
  docker rm -f $(docker ps -a -q)

  #Deleting no longer needed containers (stopped)
  docker container prune -f

  #Deleting no longer needed images which means, that it only deletes images, which are not tagged and are not pointed on by "latest" - so no real images you can regularly use are deleted
  docker image prune -a -f

  #Delete all volumes, which are not used by any existing container ( even stopped containers do claim volumes ). 
  # This usually cleans up dangling anon-volumes of containers have been deleted long time ago. 
  # It should never delete named volumes since the containers of those should exists / be running. 
  # Be careful, ensure your stack at least is running before going with this one
  docker volume prune -f

  #Same for unused networks
  docker network prune -f

  #And finally, if you want to get rid if all the trash - to ensure nothing happens to your production, be sure all stacks are running and then run
  docker system prune -f

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Function to parse details of a given service
function ndr_ParseComposeFileServiceDetails () 
{
  if [[ $# -ne 2 ]]; then
    ndr_logWarn "Usage: ndr_ParseComposeFileServiceDetails <compose_file_path> <service_name>"
    return 1
  fi

  local compose_file=$1
  local service=$2

  # Extract container_name, image full string (name:tag)
  local container_name image_full image_name image_tag

  container_name=$(yq eval ".services.\"$service\".container_name // \"\"" "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse container_name for service '$service'"
    return 1
  fi

  image_full=$(yq eval ".services.\"$service\".image // \"\"" "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse image for service '$service'" >&2
    return 1
  fi

  # If no image defined, leave blank
  if [[ -z "$image_full" ]]; then
    image_name=""
    image_tag=""
  else
    # Split image_full into name and tag by ':'
    if [[ "$image_full" == *:* ]]; then
      image_name="${image_full%%:*}"
      image_tag="${image_full##*:}"
    else
      image_name="$image_full"
      image_tag="latest"
    fi
  fi

  # Save to global associative arrays keyed by service name
  ndr_supabase_container_service_container_names["$service"]="$container_name"
  ndr_supabase_container_service_image_names["$service"]="$image_name"
  ndr_supabase_container_service_image_tags["$service"]="$image_tag"

  return 0
}

# Function to parse top-level services and call detail parser
# Usage: ndr_ParseComposeFileServiceDetails <compose_file_path>"
function ndr_ParseComposeFileServices () 
{
  local logSectionDesc="Parsing Compose File Services"
  ndr_logSecStart "$logSectionDesc"  

  if [[ $# -ne 1 ]]; then
    ndr_logWarn "Usage: ndr_ParseComposeFileServiceDetails <compose_file_path>"
    return 1
  fi

  local compose_file=$1
  if [[ ! -f "$compose_file" ]]; then
    ndr_logError "Compose file not found: $compose_file"
    return 1
  fi

  # Clear previous contents
  ndr_supabase_container_services=()
  ndr_supabase_container_service_container_names=()
  ndr_supabase_container_service_image_names=()
  ndr_supabase_container_service_image_tags=()

  # Extract service names using yq (expects yq v4+)
  mapfile -t ndr_supabase_container_services < <(yq eval '.services | keys | .[]' "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse services from $compose_file"
    return 1
  fi

  ndr_logInfo "Found ${#ndr_supabase_container_services[@]} services in the compose file '$compose_file'."

  # For each service, call details parser
  for svc in "${ndr_supabase_container_services[@]}"; do
    ndr_ParseComposeFileServiceDetails "$compose_file" "$svc"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to parse service details for '$svc' in $compose_file"
      return 1
    fi
  done

  if  [[ ${#ndr_supabase_container_services[@]} -ne 0 ]]; then  
    echo "-----------------------------------------------------------------------------------------"
    echo "Parsed Docker Compose Services in [$compose_file]:"
    echo "-----------------------------------------------------------------------------------------"

    for svc in "${ndr_supabase_container_services[@]}"; do
      # Output
      echo "Service: $svc"
      echo "  Container Name: ${ndr_supabase_container_service_container_names[$svc]}"
      echo "  Image Name    : ${ndr_supabase_container_service_image_names[$svc]}"
      echo "  Image Tag     : ${ndr_supabase_container_service_image_tags[$svc]}"
      echo
    done

    echo "-----------------------------------------------------------------------------------------"
  fi

  ndr_logInfo "Successfully parsed Docker compose file [$compose_file] service entries."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupServiceApplication ()
{
  local logSectionDesc="Cleanup Service Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_SERVICE_HOME_LOC
  local dockerImageName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerContainerName=$NDR_SERVICE_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  local applicationHomeDir
  if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
    applicationHomeDir="${gSCRIPT_HOME_DIR}/../${containerFolder}"
  else
    applicationHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  fi

  ndr_cleanupDockerApplication "$applicationHomeDir" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupUIApplication ()
{
  local logSectionDesc="Cleanup UI Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_UI_HOME_LOC
  local dockerImageName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerContainerName=$NDR_UI_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  local applicationHomeDir
  if [[ "$gNDR_DEVOPS_MODE" == true ]]; then
    applicationHomeDir="${gSCRIPT_HOME_DIR}/../${containerFolder}"
  else
    applicationHomeDir="${gNEXTDR_HOME_DIR}/${containerFolder}"
  fi

  ndr_cleanupDockerApplication "$applicationHomeDir" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_createDockerBridgeNetwork ()
{
  local logSectionDesc="Create Docker Bridge Network"
  ndr_logSecStart "$logSectionDesc"

  if ! docker network ls --format '{{.Name}}' | grep -q "^${NDR_DOCKER_BRIDGE_NETWORK_NAME}$"; then
    ndr_logInfo "Creating Docker network: ${NDR_DOCKER_BRIDGE_NETWORK_NAME}"
    docker network create "${NDR_DOCKER_BRIDGE_NETWORK_NAME}"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to create Docker network '${NDR_DOCKER_BRIDGE_NETWORK_NAME}'. Please check your Docker installation and permissions."
      return 1
    fi
  else
    ndr_logInfo "Docker network '${NDR_DOCKER_BRIDGE_NETWORK_NAME}' already exists"
  fi

  ndr_logInfo "Docker bridge network created."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <image_name> <image_version> <repo_name> <repo_type>
function ndr_DownloadDockerImage ()
{
  local logSectionDesc="Downloading Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  local imageRepoType="$4"
  
  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: ndr_DownloadDockerImage <image_name> <image_version> <repo_name> <repo_type>"
    return 1
  fi

  if [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_GIT ]]; then
    ndr_logWarn "Mode not supported"
  
  elif  [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB ]]; then
    
    docker login -u "$NDR_DOCKER_REPO_ACCOUNT" -p "$NDR_DOCKER_REPO_ACCESS_TOKEN"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker login failed for [$NDR_DOCKER_REPO_ACCOUNT]."
      return 1
    fi
    ndr_logInfo "Docker login success for account [$NDR_DOCKER_REPO_ACCOUNT]"

    # raw image base name: supabase/studio, encoded base name: supabase_NDR_DELIM_studio
    #local encodedDockerImageBaseName=$(echo "$dockerImageBaseName" | sed "s|${NDR_DOCKER_MODULE_NAME_DELIMITER_RAW}|${NDR_DOCKER_MODULE_NAME_DELIMITER_MONIKER}|g")
    # repo tag format: <account>/<repo_name>:<{image_base_name}_{image_version}>
    #local repoImageTag="$NDR_DOCKER_REPO_ACCOUNT/$imageRepoName:${encodedDockerImageBaseName}_${dockerImageVersion}"
    # transform local to remote image format
    local repoImageTag
    repoImageTag=$(ndr_LocalToRemoteRepoImageName "$dockerImageBaseName" "$dockerImageVersion" "$imageRepoName")
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to convert image name [$dockerImageName]."
      return 1
    fi

    # pull image from repo
    docker pull "$repoImageTag"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker pull failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker pull success for [$repoImageTag]."

    # check if the newly pulled Docker image is present
    ndr_verifyDockerImageExists "$repoImageTag"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker image verification failed for local copy of remote tag [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker remote repo image tag found [$repoImageTag]."

    # decode any encoded monikers in the case of supabase images.
    # decoded raw image base name: supabase/studio, encoded base name: supabase_NDR_DELIM_studio
    # not needed
    #local decodedDockerImageBaseName=$(echo "$encodedDockerImageBaseName" | sed "s|${NDR_DOCKER_MODULE_NAME_DELIMITER_MONIKER}|${NDR_DOCKER_MODULE_NAME_DELIMITER_RAW}|g")

    # re tag it from remote format to local.
    docker tag "$repoImageTag" "$dockerImageName"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker tag failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker tag success for [$repoImageTag] to [$dockerImageName]."
    
    # cleanup remote repo tag reference to local image.
    ndr_logInfo "Removing remote repo tag [$repoImageTag] from local image [$dockerImageName]"
    docker rmi "$repoImageTag"
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker rmi failed for [$repoImageTag]."
      #return 1
    fi

  else
    ndr_logError "Error, unknown Docker image repo mode."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Converts from raw image base name values: <image_base_name>, <version>, <repo_name> to encoded base name. eg: <repo_account>/<repo_name>:<moniker_image_base_name>_<version>
# Will also convert and image base name with delim to moniker format. eg. raw: supabase/studio to moniker: supabase_NDR_DELIM_studio
# usage: function <image_name> <image_version> <repo_name>
# outputs converted image name to stdout
function ndr_LocalToRemoteRepoImageName ()
{
  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  
  # Validate argument count
  if [[ $# -lt 3 ]]; then
    ndr_logError "Usage: ndr_LocalToRemoteRepoImageName <image_name> <image_version> <repo_name>"
    return 1
  fi

  # raw image base name: supabase/studio, encoded base name: supabase_NDR_DELIM_studio
  local encodedDockerImageBaseName=$(echo "$dockerImageBaseName" | sed "s|${NDR_DOCKER_MODULE_NAME_DELIMITER_RAW}|${NDR_DOCKER_MODULE_NAME_DELIMITER_MONIKER}|g")

  # repo tag format: <account>/<repo_name>:<{image_base_name}_{image_version}>
  local repoImageTag="$NDR_DOCKER_REPO_ACCOUNT/$imageRepoName:${encodedDockerImageBaseName}_${dockerImageVersion}"

  echo "$repoImageTag"

  return 0
}

function ndr_RemoteRepoImageExists ()
{
  local logSectionDesc="Remote Repo Image Check"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  
  # Validate argument count
  if [[ $# -lt 3 ]]; then
    ndr_logError "Usage: ndr_RemoteRepoImageExists <image_name> <image_version> <repo_name>"
    return 1
  fi

  # transform local to remote image format
  local remoteRepoImageName
  remoteRepoImageName=$(ndr_LocalToRemoteRepoImageName "$dockerImageBaseName" "$dockerImageVersion" "$imageRepoName")
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to convert image name [$dockerImageName]."
    return 1
  fi

  docker login -u "$NDR_DOCKER_REPO_ACCOUNT" -p "$NDR_DOCKER_REPO_ACCESS_TOKEN"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker login failed for [$NDR_DOCKER_REPO_ACCOUNT]."
    return 1
  fi
  ndr_logInfo "Docker login success for account [$NDR_DOCKER_REPO_ACCOUNT]"

  docker manifest inspect "$remoteRepoImageName" >/dev/null 2>&1
  local exists=$?
  if [[ $exists -eq 0 ]]; then
    ndr_logInfo "Image [$remoteRepoImageName] exists"
  else
    ndr_logInfo "Image [$remoteRepoImageName] does not exist"
  fi

  ndr_logSecEnd "$logSectionDesc"

  return $exists
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
